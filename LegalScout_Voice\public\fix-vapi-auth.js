/**
 * Fix Vapi Authentication Issue
 * 
 * Fixes the "bad sigauthz token" error that's preventing calls from connecting.
 */

console.log('[FixVapiAuth] Fixing Vapi authentication issue...');

// 1. Ensure correct API key is used
function fixVapiApiKey() {
  console.log('[FixVapiAuth] Checking Vapi API key configuration...');
  
  // Override Vapi constructor to ensure correct key
  const originalVapi = window.Vapi;
  if (originalVapi) {
    window.Vapi = function(apiKey) {
      // Use the correct public key
      const correctApiKey = '310f0d43-27c2-47a5-a76d-e55171d024f7';
      
      if (!apiKey || apiKey !== correctApiKey) {
        console.warn('[FixVapiAuth] ⚠️ Incorrect API key detected, using correct one');
        apiKey = correctApiKey;
      }
      
      console.log('[FixVapiAuth] ✅ Using correct Vapi API key:', apiKey.substring(0, 8) + '...');
      
      return new originalVapi(apiKey);
    };
    
    // Copy static properties
    Object.setPrototypeOf(window.Vapi, originalVapi);
    Object.assign(window.Vapi, originalVapi);
  }
}

// 2. Fix environment variables
function fixEnvironmentVariables() {
  console.log('[FixVapiAuth] Ensuring correct environment variables...');
  
  // Set correct Vapi keys
  const correctPublicKey = '310f0d43-27c2-47a5-a76d-e55171d024f7';
  const correctPrivateKey = '6734febc-fc65-4669-93b0-929b31ff6564';
  
  // Update window variables
  window.VITE_VAPI_PUBLIC_KEY = correctPublicKey;
  window.VITE_VAPI_SECRET_KEY = correctPrivateKey;
  
  // Update import.meta.env if it exists
  if (window.import && window.import.meta && window.import.meta.env) {
    window.import.meta.env.VITE_VAPI_PUBLIC_KEY = correctPublicKey;
    window.import.meta.env.VITE_VAPI_SECRET_KEY = correctPrivateKey;
  }
  
  console.log('[FixVapiAuth] ✅ Environment variables updated');
}

// 3. Monitor and fix call requests
function fixCallRequests() {
  console.log('[FixVapiAuth] Setting up call request monitoring...');
  
  const originalFetch = window.fetch;
  window.fetch = function(url, options = {}) {
    // Check for Vapi API calls
    if (typeof url === 'string' && url.includes('api.vapi.ai')) {
      console.log('[FixVapiAuth] 📞 Intercepting Vapi API call:', url);
      
      // Ensure correct authorization header
      options.headers = options.headers || {};
      
      // Convert Headers object to plain object if needed
      if (options.headers instanceof Headers) {
        const headersObj = {};
        for (const [key, value] of options.headers.entries()) {
          headersObj[key] = value;
        }
        options.headers = headersObj;
      }
      
      // Use the correct private key for API calls
      const correctPrivateKey = '6734febc-fc65-4669-93b0-929b31ff6564';
      options.headers.Authorization = `Bearer ${correctPrivateKey}`;
      
      console.log('[FixVapiAuth] ✅ Added correct authorization header');
      
      // Log the request body for debugging
      if (options.body) {
        try {
          const body = JSON.parse(options.body);
          console.log('[FixVapiAuth] 📋 Call request body:', {
            assistantId: body.assistantId,
            hasAssistant: !!body.assistant,
            assistantName: body.assistant?.name
          });
        } catch (e) {
          console.log('[FixVapiAuth] Could not parse request body');
        }
      }
    }
    
    return originalFetch.call(this, url, options);
  };
}

// 4. Handle Vapi errors gracefully
function handleVapiErrors() {
  console.log('[FixVapiAuth] Setting up error handling...');
  
  // Monitor for authentication errors
  const originalConsoleError = console.error;
  console.error = function(...args) {
    const message = args.join(' ');
    
    if (message.includes('bad sigauthz token')) {
      console.log('[FixVapiAuth] ❌ Authentication error detected - this indicates API key issues');
      
      // Show user-friendly error
      const errorDiv = document.createElement('div');
      errorDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #ff4444;
        color: white;
        padding: 15px;
        border-radius: 8px;
        z-index: 10000;
        max-width: 350px;
        font-family: Arial, sans-serif;
      `;
      errorDiv.innerHTML = `
        <strong>Call Authentication Failed</strong><br>
        There's an issue with the API configuration. Please try again in a moment.
        <button onclick="this.parentElement.remove()" style="float: right; background: none; border: none; color: white; cursor: pointer; font-size: 18px;">×</button>
      `;
      document.body.appendChild(errorDiv);
      
      // Auto-remove after 10 seconds
      setTimeout(() => {
        if (errorDiv.parentElement) {
          errorDiv.remove();
        }
      }, 10000);
    }
    
    return originalConsoleError.apply(console, args);
  };
}

// 5. Verify assistant configuration
function verifyAssistantConfig() {
  console.log('[FixVapiAuth] Verifying assistant configuration...');
  
  // Check if the correct assistant ID is being used
  const correctAssistantId = 'f9b97d13-f9c4-40af-a660-62ba5925ff2a';
  
  // Monitor for assistant usage
  const originalFetch = window.fetch;
  window.fetch = function(url, options = {}) {
    if (typeof url === 'string' && url.includes('api.vapi.ai/call/web')) {
      if (options.body) {
        try {
          const body = JSON.parse(options.body);
          
          if (body.assistantId && body.assistantId !== correctAssistantId) {
            console.warn('[FixVapiAuth] ⚠️ Using different assistant ID:', body.assistantId);
            console.log('[FixVapiAuth] Expected assistant ID:', correctAssistantId);
          } else if (body.assistantId === correctAssistantId) {
            console.log('[FixVapiAuth] ✅ Using correct assistant ID');
          }
        } catch (e) {
          // Ignore parsing errors
        }
      }
    }
    
    return originalFetch.call(this, url, options);
  };
}

// 6. Add retry mechanism for failed calls
function addRetryMechanism() {
  console.log('[FixVapiAuth] Adding call retry mechanism...');
  
  let retryCount = 0;
  const maxRetries = 2;
  
  // Monitor for call failures and retry
  window.addEventListener('vapiError', (event) => {
    if (event.detail?.errorMsg === 'bad sigauthz token' && retryCount < maxRetries) {
      retryCount++;
      console.log(`[FixVapiAuth] 🔄 Retrying call (attempt ${retryCount}/${maxRetries})...`);
      
      // Wait a moment then retry
      setTimeout(() => {
        // Trigger a new call attempt
        const callButton = document.querySelector('button[onclick*="call"], button[class*="call"]');
        if (callButton) {
          console.log('[FixVapiAuth] 🔄 Triggering retry...');
          callButton.click();
        }
      }, 2000);
    } else if (retryCount >= maxRetries) {
      console.error('[FixVapiAuth] ❌ Max retries reached, call failed permanently');
    }
  });
}

// 7. Main initialization
function initializeVapiAuthFix() {
  console.log('[FixVapiAuth] Initializing Vapi authentication fixes...');
  
  // Apply all fixes
  fixEnvironmentVariables();
  fixVapiApiKey();
  fixCallRequests();
  handleVapiErrors();
  verifyAssistantConfig();
  addRetryMechanism();
  
  console.log('[FixVapiAuth] ✅ All authentication fixes applied');
  
  // Add manual test function
  window.testVapiAuth = function() {
    console.log('[FixVapiAuth] 🧪 Testing Vapi authentication...');
    
    const publicKey = window.VITE_VAPI_PUBLIC_KEY || window.import?.meta?.env?.VITE_VAPI_PUBLIC_KEY;
    const privateKey = window.VITE_VAPI_SECRET_KEY || window.import?.meta?.env?.VITE_VAPI_SECRET_KEY;
    
    console.log('Public Key:', publicKey?.substring(0, 8) + '...');
    console.log('Private Key:', privateKey?.substring(0, 8) + '...');
    console.log('Vapi SDK loaded:', !!window.Vapi);
    
    return {
      publicKey: !!publicKey,
      privateKey: !!privateKey,
      vapiLoaded: !!window.Vapi,
      correctPublicKey: publicKey === '310f0d43-27c2-47a5-a76d-e55171d024f7',
      correctPrivateKey: privateKey === '6734febc-fc65-4669-93b0-929b31ff6564'
    };
  };
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeVapiAuthFix);
} else {
  initializeVapiAuthFix();
}

console.log('[FixVapiAuth] Vapi authentication fix script loaded');
console.log('[FixVapiAuth] 💡 Run window.testVapiAuth() to verify configuration');
