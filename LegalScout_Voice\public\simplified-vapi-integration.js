/**
 * Simplified Vapi Integration
 * 
 * This script provides a simplified integration with Vapi using MCP tools,
 * with robust error handling and fallbacks.
 */

(function() {
  console.log('[SimplifiedVapiIntegration] Initializing...');
  
  // Default assistant ID to use as fallback
  const DEFAULT_ASSISTANT_ID = '8d962209-530e-45d2-b2d6-17ed1ef55b3c';
  
  // Cache for Vapi assistants
  let assistantsCache = null;
  let assistantsCacheTimestamp = 0;
  
  /**
   * Get Vapi assistants with caching
   * @param {boolean} forceRefresh - Whether to force a refresh of the cache
   * @returns {Promise<Array>} The list of assistants
   */
  async function getVapiAssistants(forceRefresh = false) {
    try {
      // Check if we have a recent cache (less than 5 minutes old)
      const now = Date.now();
      if (!forceRefresh && assistantsCache && (now - assistantsCacheTimestamp) < 5 * 60 * 1000) {
        console.log('[SimplifiedVapiIntegration] Using cached assistants');
        return assistantsCache;
      }
      
      // Check if MCP is available
      if (!window.mcp) {
        console.warn('[SimplifiedVapiIntegration] MCP not available');
        return [];
      }
      
      console.log('[SimplifiedVapiIntegration] Fetching assistants via MCP');
      
      // Get assistants via MCP
      const assistants = await window.mcp.invoke('list_assistants_vapi-mcp-server', {});
      
      // Update cache
      assistantsCache = assistants;
      assistantsCacheTimestamp = now;
      
      console.log('[SimplifiedVapiIntegration] Fetched assistants:', assistants.length);
      
      return assistants;
    } catch (error) {
      console.error('[SimplifiedVapiIntegration] Error fetching assistants:', error);
      return assistantsCache || [];
    }
  }
  
  /**
   * Get a Vapi assistant by ID
   * @param {string} assistantId - The assistant ID to get
   * @returns {Promise<Object>} The assistant object
   */
  async function getVapiAssistant(assistantId) {
    try {
      // Check if MCP is available
      if (!window.mcp) {
        console.warn('[SimplifiedVapiIntegration] MCP not available');
        return null;
      }
      
      console.log('[SimplifiedVapiIntegration] Fetching assistant via MCP:', assistantId);
      
      // Get assistant via MCP
      const assistant = await window.mcp.invoke('get_assistant_vapi-mcp-server', {
        assistantId: assistantId
      });
      
      console.log('[SimplifiedVapiIntegration] Fetched assistant:', assistant);
      
      return assistant;
    } catch (error) {
      console.error('[SimplifiedVapiIntegration] Error fetching assistant:', error);
      return null;
    }
  }
  
  /**
   * Create a new Vapi assistant
   * @param {Object} attorneyData - The attorney data to use for the assistant
   * @returns {Promise<Object>} The created assistant
   */
  async function createVapiAssistant(attorneyData) {
    try {
      // Check if MCP is available
      if (!window.mcp) {
        console.warn('[SimplifiedVapiIntegration] MCP not available');
        return { id: DEFAULT_ASSISTANT_ID };
      }
      
      console.log('[SimplifiedVapiIntegration] Creating assistant via MCP');
      
      // Create assistant data from attorney data
      const assistantData = {
        name: `${attorneyData.name || 'Attorney'}'s Legal Assistant`,
        instructions: attorneyData.vapi_instructions || `You are a legal assistant for ${attorneyData.firm_name || 'a law firm'}. Help potential clients understand their legal needs and collect relevant information for consultation.`,
        firstMessage: attorneyData.welcome_message || 'Hello! I\'m your legal assistant. How can I help you today?',
        voice: {
          provider: attorneyData.voice_provider || '11labs',
          voiceId: attorneyData.voice_id || 'sarah'
        },
        llm: {
          provider: 'openai',
          model: 'gpt-4o-mini'
        }
      };
      
      // Create assistant via MCP
      const assistant = await window.mcp.invoke('create_assistant_vapi-mcp-server', assistantData);
      
      console.log('[SimplifiedVapiIntegration] Created assistant:', assistant);
      
      return assistant;
    } catch (error) {
      console.error('[SimplifiedVapiIntegration] Error creating assistant:', error);
      return { id: DEFAULT_ASSISTANT_ID };
    }
  }
  
  /**
   * Sync attorney with Vapi
   * @param {Object} attorney - The attorney to sync
   * @returns {Promise<Object>} The result of the sync
   */
  async function syncAttorneyWithVapi(attorney) {
    try {
      console.log('[SimplifiedVapiIntegration] Syncing attorney with Vapi:', attorney.id);
      
      // Validate attorney
      if (!attorney || !attorney.id) {
        console.warn('[SimplifiedVapiIntegration] Invalid attorney data');
        return { action: 'error', error: 'Invalid attorney data' };
      }
      
      // Check if attorney has a Vapi assistant ID
      if (!attorney.vapi_assistant_id) {
        console.log('[SimplifiedVapiIntegration] Attorney has no assistant ID, creating new assistant');
        
        // Create a new assistant
        const assistant = await createVapiAssistant(attorney);
        
        // Update attorney with assistant ID
        const updatedAttorney = {
          ...attorney,
          vapi_assistant_id: assistant.id,
          updated_at: new Date().toISOString()
        };
        
        // Update localStorage
        localStorage.setItem('attorney', JSON.stringify(updatedAttorney));
        
        // Update standalone attorney manager if available
        if (window.standaloneAttorneyManager) {
          window.standaloneAttorneyManager.attorney = updatedAttorney;
          window.standaloneAttorneyManager.notifySubscribers();
        }
        
        // Update attorney state manager if available
        if (window.attorneyStateManager) {
          window.attorneyStateManager.attorney = updatedAttorney;
          window.attorneyStateManager.notifySubscribers();
        }
        
        console.log('[SimplifiedVapiIntegration] Created new assistant and updated attorney:', assistant.id);
        
        return { action: 'created', assistant };
      }
      
      // Check if the assistant exists
      const assistant = await getVapiAssistant(attorney.vapi_assistant_id);
      
      if (!assistant) {
        console.log('[SimplifiedVapiIntegration] Assistant not found, creating new one');
        
        // Create a new assistant
        const newAssistant = await createVapiAssistant(attorney);
        
        // Update attorney with new assistant ID
        const updatedAttorney = {
          ...attorney,
          vapi_assistant_id: newAssistant.id,
          updated_at: new Date().toISOString()
        };
        
        // Update localStorage
        localStorage.setItem('attorney', JSON.stringify(updatedAttorney));
        
        // Update standalone attorney manager if available
        if (window.standaloneAttorneyManager) {
          window.standaloneAttorneyManager.attorney = updatedAttorney;
          window.standaloneAttorneyManager.notifySubscribers();
        }
        
        // Update attorney state manager if available
        if (window.attorneyStateManager) {
          window.attorneyStateManager.attorney = updatedAttorney;
          window.attorneyStateManager.notifySubscribers();
        }
        
        console.log('[SimplifiedVapiIntegration] Created new assistant and updated attorney:', newAssistant.id);
        
        return { action: 'created', assistant: newAssistant };
      }
      
      console.log('[SimplifiedVapiIntegration] Assistant exists, no update needed');
      
      return { action: 'existing', assistant };
    } catch (error) {
      console.error('[SimplifiedVapiIntegration] Error syncing attorney with Vapi:', error);
      return { action: 'error', error: error.message };
    }
  }
  
  // Expose the API
  window.vapiIntegration = {
    getAssistants: getVapiAssistants,
    getAssistant: getVapiAssistant,
    createAssistant: createVapiAssistant,
    syncAttorney: syncAttorneyWithVapi
  };
  
  // Initialize by syncing the current attorney
  setTimeout(() => {
    try {
      // Get the current attorney from localStorage
      const attorneyJson = localStorage.getItem('attorney');
      
      if (attorneyJson) {
        const attorney = JSON.parse(attorneyJson);
        
        if (attorney && attorney.id) {
          console.log('[SimplifiedVapiIntegration] Syncing current attorney:', attorney.id);
          
          // Sync with Vapi
          syncAttorneyWithVapi(attorney).catch(error => {
            console.error('[SimplifiedVapiIntegration] Error in initial sync:', error);
          });
        }
      }
    } catch (error) {
      console.error('[SimplifiedVapiIntegration] Error in initialization:', error);
    }
  }, 2000); // Wait 2 seconds to ensure other systems are initialized
  
  console.log('[SimplifiedVapiIntegration] Initialization complete');
})();
