# LegalScout Technical Architecture

## 🎯 MVP Status: PRODUCTION READY (June 3, 2025)

### System Health Overview
- ✅ **Authentication Flow**: Stable, routes correctly to dashboard
- ✅ **Profile Management**: Consolidated, no duplicates, reliable loading
- ✅ **Vapi Integration**: Single assistant, no duplicate creation, working voice calls
- ✅ **Data Sync**: One-way pattern implemented (UI → Supabase → Vapi)
- ✅ **Error Prevention**: Creation guards, cooldown periods, validation checks

### Critical Fixes Applied
1. **Duplicate Assistant Prevention**: Implemented `assistantCreationGuard.js` utility
2. **Profile Consolidation**: Enhanced `AttorneyProfileManager.js` with duplicate detection
3. **Routing Stability**: Fixed authentication flow in `App.jsx` and `AuthCallback.jsx`
4. **Sync Pattern**: Disabled auto-sync during initialization, manual triggers only

## System Architecture Overview

LegalScout is built on a modern web architecture with a React frontend, Supabase backend, and Vapi.ai integration for voice capabilities. The system uses the Model Context Protocol (MCP) for service integrations and follows a component-based design pattern.

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│  React Frontend │────▶│  Vapi.ai API    │────▶│  OpenAI/Claude  │
│                 │     │                 │     │                 │
└────────┬────────┘     └─────────────────┘     └─────────────────┘
         │
         │
         ▼
┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │
│    Supabase     │────▶│  Storage Bucket │
│                 │     │                 │
└─────────────────┘     └─────────────────┘
```

## Frontend Architecture

### Component Structure

The frontend follows a hierarchical component structure:

```
App
├── AnimatedBackground
├── Navbar
├── Routes
│   ├── Home
│   │   └── VapiCall
│   ├── Dashboard
│   │   ├── ProfileTab
│   │   ├── AgentTab
│   │   ├── CustomFieldsTab
│   │   ├── AutomationTab
│   │   ├── ConsultationsTab
│   │   └── IntegrationsTab
│   ├── SimpleDemoPage
│   ├── PreviewPage
│   └── AboutPage
└── AuthOverlay
```

### State Management

- **React Context API** for global state (Auth, Theme)
- **Local component state** with useState for component-specific state
- **localStorage** for persistent settings and caching

### Key Components

1. **VapiCall**
   - Manages voice interaction with Vapi.ai
   - Handles real-time transcription and responses
   - Updates UI based on call state

2. **Dashboard**
   - Container for attorney dashboard
   - Manages tab navigation
   - Handles authentication state

3. **MapView / GlobeDossierView**
   - Visualizes attorney locations
   - Provides interactive map interface
   - Supports both 2D and 3D visualization

## Backend Architecture

### Supabase Integration

The application uses Supabase for:
- **Database**: PostgreSQL database for storing attorney profiles, call records, etc.
- **Authentication**: User authentication and session management
- **Storage**: File storage for logos, voice samples, etc.
- **Row-Level Security**: Data access control based on user roles

### Database Schema

```
┌─────────────────┐     ┌─────────────────┐
│    attorneys    │─────│  call_records   │
├─────────────────┤     ├─────────────────┤
│ id              │     │ id              │
│ subdomain       │     │ assistant_id    │
│ firm_name       │     │ created_at      │
│ logo_url        │     │ client_info     │
│ vapi_assistant_id│     │ summary        │
└─────────────────┘     └─────────────────┘
        │
        │
        ▼
┌─────────────────┐
│  custom_fields  │
├─────────────────┤
│ id              │
│ attorney_id     │
│ field_name      │
│ field_type      │
└─────────────────┘
```

## Voice AI Integration

### Vapi.ai Architecture

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│  VapiCall      │────▶│  Vapi SDK       │────▶│  Vapi API       │
│  Component      │     │                 │     │                 │
└─────────────────┘     └─────────────────┘     └─────────────────┘
                                                        │
                                                        │
                                                        ▼
                                         ┌─────────────────────────────┐
                                         │                             │
                                         │  LLM (GPT-4o, Claude, etc)  │
                                         │                             │
                                         └─────────────────────────────┘
```

### MCP Integration

The Model Context Protocol (MCP) is used to integrate with various services:

```
┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │
│  MCP Client     │────▶│  Vapi MCP Server│
│                 │     │                 │
└─────────────────┘     └─────────────────┘
        │
        │
        ▼
┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │
│  Gmail API      │     │  Other Services │
│                 │     │                 │
└─────────────────┘     └─────────────────┘
```

## Authentication Flow

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│  User Login     │────▶│  Gmail OAuth    │────▶│  Supabase Auth  │
│                 │     │                 │     │                 │
└─────────────────┘     └─────────────────┘     └─────────────────┘
                                                        │
                                                        │
                                                        ▼
                                         ┌─────────────────────────────┐
                                         │                             │
                                         │  Attorney Record Creation   │
                                         │                             │
                                         └─────────────────────────────┘
```

## Subdomain System Architecture

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│  DNS Request    │────▶│  Vercel Router  │────▶│  Subdomain      │
│  (*.legalscout.net)   │                 │     │  Handler        │
└─────────────────┘     └─────────────────┘     └─────────────────┘
                                                        │
                                                        │
                                                        ▼
                                         ┌─────────────────────────────┐
                                         │                             │
                                         │  Attorney Config Loader     │
                                         │                             │
                                         └─────────────────────────────┘
```

## Data Flow

### Voice Consultation Flow

1. User initiates call through UI
2. VapiCall component creates connection to Vapi.ai
3. Voice input is sent to Vapi for transcription
4. Transcribed text is processed by LLM (GPT-4o, Claude)
5. Response is converted to speech and played to user
6. Conversation data is stored and analyzed
7. Attorney recommendations are generated based on conversation

### Attorney Dashboard Flow

1. Attorney logs in via Gmail OAuth
2. Authentication handled by Supabase
3. Attorney profile loaded from database
4. Dashboard components render based on profile data
5. Updates to profile saved to Supabase
6. Vapi assistant configuration updated via MCP

## API Integration Points

### Vapi.ai API
- **Assistant Creation**: Create custom assistants for attorneys
- **Call Handling**: Manage voice calls and transcription
- **Voice Configuration**: Set up custom voices and parameters

### Supabase API
- **Data CRUD**: Create, read, update, delete operations on database
- **Authentication**: User login, session management
- **Storage**: File upload and retrieval

### MCP API
- **Vapi Control**: Programmatic control of Vapi assistants
- **Gmail Integration**: Email communication and authentication
- **Service Orchestration**: Coordinate between different services

## Deployment Architecture

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│  GitHub Repo    │────▶│  Vercel CI/CD   │────▶│  Vercel Edge    │
│                 │     │                 │     │  Network        │
└─────────────────┘     └─────────────────┘     └─────────────────┘
                                                        │
                                                        │
                                                        ▼
                                         ┌─────────────────────────────┐
                                         │                             │
                                         │  legalscout.ai              │
                                         │  *.legalscout.net           │
                                         └─────────────────────────────┘
```

## Security Architecture

- **Authentication**: Gmail OAuth for secure login
- **Authorization**: Row-level security in Supabase
- **Data Protection**: HTTPS for all communications
- **API Security**: Token-based authentication for all APIs
- **Environment Variables**: Secure storage of sensitive keys

## Performance Optimization

- **Code Splitting**: Lazy loading of components
- **Asset Optimization**: Compressed images and assets
- **Caching Strategy**: Local storage for frequently accessed data
- **Lazy Loading**: Components loaded only when needed

## Error Handling Strategy

- **Global Error Boundaries**: Catch and handle React errors
- **API Error Handling**: Graceful degradation on API failures
- **Fallback Mechanisms**: Default values when data is unavailable
- **User Feedback**: Clear error messages and recovery options

## Testing Architecture

- **Component Testing**: Unit tests for React components
- **Integration Testing**: Testing component interactions
- **API Mocking**: Mock API responses for testing
- **End-to-End Testing**: Complete user flow testing
