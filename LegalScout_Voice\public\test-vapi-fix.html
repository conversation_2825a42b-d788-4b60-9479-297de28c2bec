<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vapi Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-result {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>🔧 Vapi URL Fix Test</h1>
    
    <div class="test-container">
        <h2>Vapi SDK Loading Test</h2>
        <button onclick="testVapiSDKLoading()">Test Vapi SDK Loading</button>
        <button onclick="testVapiInstanceCreation()">Test Vapi Instance Creation</button>
        <button onclick="testVapiCallStart()">Test Vapi Call Start (Mock)</button>
        <div id="test-results"></div>
    </div>

    <div class="test-container">
        <h2>Console Logs</h2>
        <div id="console-logs"></div>
    </div>

    <script>
        const resultsDiv = document.getElementById('test-results');
        const logsDiv = document.getElementById('console-logs');

        function addResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            resultsDiv.appendChild(div);
        }

        function addLog(message) {
            const div = document.createElement('div');
            div.innerHTML = `<pre>${new Date().toISOString()}: ${message}</pre>`;
            logsDiv.appendChild(div);
            console.log(message);
        }

        async function testVapiSDKLoading() {
            addResult('🧪 Testing Vapi SDK Loading...', 'info');
            
            try {
                // Check if Vapi is already loaded
                if (window.Vapi) {
                    addResult('✅ Vapi SDK already loaded', 'success');
                    addLog(`Vapi constructor type: ${typeof window.Vapi}`);
                    return true;
                }

                // Try to load from CDN
                addResult('📦 Loading Vapi SDK from CDN...', 'info');
                
                const script = document.createElement('script');
                script.src = 'https://cdn.vapi.ai/web-sdk@2.2.2/dist/web-sdk.js';
                
                const loadPromise = new Promise((resolve, reject) => {
                    script.onload = () => {
                        if (window.Vapi) {
                            addResult('✅ Vapi SDK loaded successfully from CDN', 'success');
                            addLog(`Vapi constructor available: ${typeof window.Vapi}`);
                            resolve(true);
                        } else {
                            addResult('❌ Vapi SDK script loaded but Vapi not available', 'error');
                            reject(new Error('Vapi not available after script load'));
                        }
                    };
                    
                    script.onerror = () => {
                        addResult('❌ Failed to load Vapi SDK from CDN', 'error');
                        reject(new Error('Failed to load Vapi SDK'));
                    };
                });
                
                document.head.appendChild(script);
                return await loadPromise;
                
            } catch (error) {
                addLog(`Error: ${error.message}`);
                addResult(`❌ Vapi SDK loading failed: ${error.message}`, 'error');
                return false;
            }
        }

        async function testVapiInstanceCreation() {
            addResult('🧪 Testing Vapi Instance Creation...', 'info');
            
            try {
                // Ensure SDK is loaded first
                const sdkLoaded = await testVapiSDKLoading();
                if (!sdkLoaded) {
                    throw new Error('Vapi SDK not loaded');
                }

                const apiKey = '310f0d43-27c2-47a5-a76d-e55171d024f7'; // Public key
                addLog(`Creating Vapi instance with API key: ${apiKey.substring(0, 8)}...`);
                
                // Test the fix: create instance with minimal configuration
                const vapiInstance = new window.Vapi(apiKey);
                
                if (vapiInstance) {
                    addResult('✅ Vapi instance created successfully', 'success');
                    addLog(`Instance methods: start=${typeof vapiInstance.start}, stop=${typeof vapiInstance.stop}`);
                    
                    // Test that the instance has required methods
                    if (typeof vapiInstance.start === 'function' && typeof vapiInstance.stop === 'function') {
                        addResult('✅ Vapi instance has required methods', 'success');
                        return vapiInstance;
                    } else {
                        addResult('⚠️ Vapi instance missing required methods', 'error');
                        return null;
                    }
                } else {
                    addResult('❌ Failed to create Vapi instance', 'error');
                    return null;
                }
                
            } catch (error) {
                addLog(`Error: ${error.message}`);
                addResult(`❌ Vapi instance creation failed: ${error.message}`, 'error');
                return null;
            }
        }

        async function testVapiCallStart() {
            addResult('🧪 Testing Vapi Call Start (Mock)...', 'info');
            
            try {
                const vapiInstance = await testVapiInstanceCreation();
                if (!vapiInstance) {
                    throw new Error('No Vapi instance available');
                }

                const assistantId = 'f9b97d13-f9c4-40af-a660-62ba5925ff2a';
                addLog(`Testing call start with assistant ID: ${assistantId}`);
                
                // Mock the call start to test URL configuration
                // We'll intercept the fetch to see if the URL is properly formatted
                const originalFetch = window.fetch;
                let fetchCalled = false;
                let fetchUrl = '';
                
                window.fetch = function(url, options) {
                    fetchCalled = true;
                    fetchUrl = url;
                    addLog(`Fetch called with URL: ${url}`);
                    
                    // Check if URL is properly formatted
                    if (typeof url === 'string' && url.includes('api.vapi.ai')) {
                        addResult('✅ Fetch URL is properly formatted as string', 'success');
                    } else if (typeof url === 'object') {
                        addResult('❌ Fetch URL is an object (this was the bug!)', 'error');
                        addLog(`URL object: ${JSON.stringify(url)}`);
                    } else {
                        addResult(`⚠️ Unexpected URL type: ${typeof url}`, 'error');
                    }
                    
                    // Restore original fetch and return a mock response
                    window.fetch = originalFetch;
                    return Promise.resolve({
                        ok: true,
                        json: () => Promise.resolve({ id: 'mock-call-id' })
                    });
                };
                
                // Try to start the call
                try {
                    await vapiInstance.start(assistantId);
                    addResult('✅ Call start method executed without URL errors', 'success');
                } catch (callError) {
                    if (callError.message.includes('url should be a string')) {
                        addResult('❌ URL object error still present', 'error');
                    } else {
                        addResult(`ℹ️ Call failed for other reasons: ${callError.message}`, 'info');
                    }
                }
                
                if (!fetchCalled) {
                    addResult('ℹ️ Fetch was not called (may be expected)', 'info');
                }
                
            } catch (error) {
                addLog(`Error: ${error.message}`);
                addResult(`❌ Call start test failed: ${error.message}`, 'error');
            }
        }

        // Auto-run basic test on page load
        window.addEventListener('load', () => {
            addResult('🔧 Vapi Fix Test Page Loaded', 'info');
            addLog('Test page initialized');
        });
    </script>
</body>
</html>
