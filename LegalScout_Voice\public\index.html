<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LegalScout</title>
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap"
      rel="stylesheet"
    />

    <!-- DEVELOPMENT CSP FIX: Allow localhost API server connections -->
    <script src="/dev-csp-fix.js"></script>

    <!-- EMERGENCY API KEY FIX: Must load first to fix API key issues -->
    <script src="/emergency-api-key-fix.js"></script>

    <!-- CRITICAL PRODUCTION FIX: Must load first to fix all production issues -->
    <script src="/critical-production-fix.js"></script>

    <!-- Load Three.js and Three-Globe via CDN for reliable loading -->
    <script src="https://unpkg.com/three@0.174.0/build/three.min.js"></script>
    <script src="https://unpkg.com/three@0.174.0/examples/js/controls/OrbitControls.js"></script>
    <script src="https://unpkg.com/three-globe@2.42.1/dist/three-globe.min.js"></script>
    <!-- Essential fixes only - root cause fixed in vapiMcpService.js -->
    <script src="/fix-s-catch-standalone.js"></script>
    <!-- ULTRA-THINKING Call Termination Fix -->
    <script src="/fix-call-termination.js"></script>
    <!-- Call Functionality Test (for diagnostics) -->
    <script src="/test-call-functionality.js"></script>
    <!-- ULTRA-THINKING Vapi Call Diagnostics (comprehensive testing) -->
    <script src="/vapi-call-diagnostics.js"></script>
    <!-- Elegant Assistant Creation Solution -->
    <script src="/headers-fix.js"></script>
    <script src="/dashboard-loading-interceptor.js"></script>
    <script src="/elegant-assistant-configuration.js"></script>
    <script src="/elegant-assistant-creation.js"></script>
    <script src="/robust-state-handler.js"></script>
    <script src="/enhanced-dashboard-integration.js"></script>
    <script src="/test-damonandlaurakost-scenario.js"></script>
    <script src="/debug-email-state.js"></script>
    <script src="/test-elegant-flow.js"></script>
    <!-- Assistant Dropdown Diagnostic -->
    <script src="/assistant-dropdown-diagnostic.js"></script>
    <!-- Authentication Diagnostic -->
    <script src="/auth-diagnostic.js"></script>
    <!-- Test Current State -->
    <script src="/test-current-state.js"></script>
    <!-- Agent Tab Debug Loop - DISABLED to prevent loops -->
    <!-- <script src="/agent-tab-debug-loop.js"></script> -->
    <!-- Vapi Connection Test - DISABLED to prevent module errors -->
    <!-- <script type="module" src="/test-vapi-connection.js"></script> -->

    <!-- 🔍 DIAGNOSTIC SCRIPTS FOR SESSION AGENT CHANGE ISSUES -->
    <script>
      // Load diagnostic scripts after all other systems are initialized
      window.addEventListener('load', () => {
        setTimeout(() => {
          console.log('🔍 Loading diagnostic test suite...');

          // Create and load diagnostic test suite
          const diagnosticScript = document.createElement('script');
          diagnosticScript.src = '/temp_files/diagnostic-test-suite.js';
          diagnosticScript.onload = () => {
            console.log('✅ Diagnostic test suite loaded');

            // Load impact assessment after diagnostics
            setTimeout(() => {
              console.log('📊 Loading impact assessment...');
              const impactScript = document.createElement('script');
              impactScript.src = '/temp_files/impact-assessment.js';
              impactScript.onload = () => {
                console.log('✅ Impact assessment loaded');
                console.log('🎯 Diagnostic suite ready. Check console for results.');
              };
              impactScript.onerror = () => {
                console.warn('⚠️ Failed to load impact assessment script');
              };
              document.head.appendChild(impactScript);
            }, 3000);
          };
          diagnosticScript.onerror = () => {
            console.warn('⚠️ Failed to load diagnostic test suite script');
          };
          document.head.appendChild(diagnosticScript);
        }, 5000); // Wait 5 seconds for all systems to initialize
      });
    </script>

    <!-- 🛡️ AUTOMATED ROBUST STATE HANDLER - Runs on page load -->
    <script>
      window.addEventListener('load', async () => {
        console.log('🛡️ PAGE LOAD: Starting automated robust state handler...');

        // Wait for the page to settle
        await new Promise(resolve => setTimeout(resolve, 2000));

        try {
          // Check if we have Supabase available
          if (typeof window.supabase === 'undefined') {
            console.log('🛡️ PAGE LOAD: Supabase not available, skipping');
            return;
          }

          // Get user from Supabase
          const { data: { user }, error: userError } = await window.supabase.auth.getUser();

          if (userError || !user?.email) {
            console.log('🛡️ PAGE LOAD: No authenticated user found, skipping');
            return;
          }

          console.log('🛡️ PAGE LOAD: User found:', user.email);

          // Check if robust state handler is available
          if (typeof window.resolveAttorneyState === 'function') {
            console.log('🛡️ PAGE LOAD: Calling robust state handler for:', user.email);

            const stateResult = await window.resolveAttorneyState(user.email);
            console.log('🛡️ PAGE LOAD: Robust state handler result:', stateResult);

            if (stateResult.success && stateResult.attorney) {
              console.log('🛡️ PAGE LOAD: ✅ Attorney state resolved successfully!');
              console.log('🛡️ PAGE LOAD: Attorney:', stateResult.attorney.firm_name, 'Assistant:', stateResult.attorney.vapi_assistant_id);

              // DISABLED: Force page refresh to prevent loops
              // setTimeout(() => {
              //   console.log('🛡️ PAGE LOAD: 🔄 Refreshing page to update UI...');
              //   window.location.reload();
              // }, 1000);
            } else {
              console.warn('🛡️ PAGE LOAD: ⚠️ Robust state handler failed:', stateResult.error);
            }
          } else {
            console.warn('🛡️ PAGE LOAD: ⚠️ Robust state handler not available');
          }
        } catch (error) {
          console.error('🛡️ PAGE LOAD: ❌ Error:', error);
        }
      });
    </script>

</head>
<body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <!-- Create React App will inject the bundle script here automatically -->
</body>
</html>