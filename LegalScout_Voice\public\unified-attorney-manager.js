/**
 * Unified Attorney Manager
 *
 * This script integrates with GlobalAttorneyCoordinator to provide
 * consistent attorney state management across all contexts.
 */

(function() {
  console.log('[UnifiedAttorneyManager] Starting unified attorney management...');

  // Wait for GlobalAttorneyCoordinator to be available
  const waitForCoordinator = () => {
    return new Promise((resolve) => {
      if (window.GlobalAttorneyCoordinator) {
        resolve(window.GlobalAttorneyCoordinator);
        return;
      }

      const checkInterval = setInterval(() => {
        if (window.GlobalAttorneyCoordinator) {
          clearInterval(checkInterval);
          resolve(window.GlobalAttorneyCoordinator);
        }
      }, 100);

      // Timeout after 10 seconds
      setTimeout(() => {
        clearInterval(checkInterval);
        console.error('[UnifiedAttorneyManager] GlobalAttorneyCoordinator not available');
        resolve(null);
      }, 10000);
    });
  };

  // Import AttorneyProfileManager from the source
  const loadAttorneyProfileManager = async () => {
    try {
      // Wait for the module system to be available
      if (typeof window.require === 'undefined' && typeof import === 'undefined') {
        console.log('[UnifiedAttorneyManager] Module system not available, creating inline implementation');
        return createInlineAttorneyProfileManager();
      }

      // Try to import the actual AttorneyProfileManager
      // This will work in development but may not work in production
      try {
        const module = await import('/src/services/AttorneyProfileManager.js');
        return new module.default();
      } catch (importError) {
        console.warn('[UnifiedAttorneyManager] Could not import AttorneyProfileManager, creating inline version');
        return createInlineAttorneyProfileManager();
      }
    } catch (error) {
      console.error('[UnifiedAttorneyManager] Error loading AttorneyProfileManager:', error);
      return createInlineAttorneyProfileManager();
    }
  };

  // Create an inline version of AttorneyProfileManager for production
  const createInlineAttorneyProfileManager = () => {
    return {
      currentAttorney: null,
      listeners: new Set(),
      isInitialized: false,

      // Initialize with subdomain-based loading
      async initialize(subdomain = null) {
        console.log('[UnifiedAttorneyManager] Initializing with subdomain:', subdomain);

        try {
          // First try to load from localStorage
          const storedAttorney = this.loadFromLocalStorage();
          if (storedAttorney && (!subdomain || storedAttorney.subdomain === subdomain)) {
            console.log('[UnifiedAttorneyManager] Loaded attorney from localStorage:', storedAttorney.id);
            this.currentAttorney = storedAttorney;
            this.isInitialized = true;
            this.notifyListeners();
            return storedAttorney;
          }

          // If localStorage doesn't have the right attorney, try Supabase
          if (subdomain && window.supabase) {
            console.log('[UnifiedAttorneyManager] Loading attorney from Supabase for subdomain:', subdomain);
            const attorney = await this.loadAttorneyBySubdomain(subdomain);
            if (attorney) {
              this.currentAttorney = attorney;
              this.saveToLocalStorage(attorney);
              this.isInitialized = true;
              this.notifyListeners();
              return attorney;
            }
          }

          console.warn('[UnifiedAttorneyManager] No attorney found for subdomain:', subdomain);
          return null;
        } catch (error) {
          console.error('[UnifiedAttorneyManager] Error during initialization:', error);

          // Fallback to localStorage if available
          const storedAttorney = this.loadFromLocalStorage();
          if (storedAttorney) {
            this.currentAttorney = storedAttorney;
            this.isInitialized = true;
            this.notifyListeners();
            return storedAttorney;
          }

          throw error;
        }
      },

      // Load attorney by subdomain from Supabase
      async loadAttorneyBySubdomain(subdomain) {
        try {
          if (!window.supabase) {
            console.warn('[UnifiedAttorneyManager] Supabase client not available');
            return null;
          }

          const { data, error } = await window.supabase
            .from('attorneys')
            .select('*')
            .eq('subdomain', subdomain)
            .single();

          if (error) {
            console.error('[UnifiedAttorneyManager] Error loading attorney by subdomain:', error);
            return null;
          }

          return data;
        } catch (error) {
          console.error('[UnifiedAttorneyManager] Error in loadAttorneyBySubdomain:', error);
          return null;
        }
      },

      // Load from localStorage (compatible with existing structure)
      loadFromLocalStorage() {
        try {
          // Try the existing attorney_profile key first
          let stored = localStorage.getItem('attorney_profile');
          if (stored) {
            const parsed = JSON.parse(stored);
            console.log('[UnifiedAttorneyManager] Loaded from attorney_profile key:', parsed.id);
            return parsed;
          }

          // Try the StandaloneAttorneyManager key as fallback
          stored = localStorage.getItem('standalone_attorney_manager_attorney');
          if (stored) {
            const parsed = JSON.parse(stored);
            console.log('[UnifiedAttorneyManager] Loaded from standalone key:', parsed.id);
            return parsed;
          }

          return null;
        } catch (error) {
          console.error('[UnifiedAttorneyManager] Error loading from localStorage:', error);
          return null;
        }
      },

      // Save to localStorage (compatible with existing structure)
      saveToLocalStorage(attorney) {
        try {
          // Save to both keys for compatibility
          localStorage.setItem('attorney_profile', JSON.stringify(attorney));
          localStorage.setItem('standalone_attorney_manager_attorney', JSON.stringify(attorney));
          localStorage.setItem('standalone_attorney_manager_attorney_id', attorney.id);
          console.log('[UnifiedAttorneyManager] Saved attorney to localStorage:', attorney.id);
        } catch (error) {
          console.error('[UnifiedAttorneyManager] Error saving to localStorage:', error);
        }
      },

      // Add listener
      addListener(callback) {
        this.listeners.add(callback);
        // Call immediately with current state
        if (this.currentAttorney) {
          callback(this.currentAttorney);
        }
      },

      // Remove listener
      removeListener(callback) {
        this.listeners.delete(callback);
      },

      // Notify all listeners
      notifyListeners() {
        this.listeners.forEach(callback => {
          try {
            callback(this.currentAttorney);
          } catch (error) {
            console.error('[UnifiedAttorneyManager] Error in listener callback:', error);
          }
        });
      },

      // Get current attorney
      getAttorney() {
        return this.currentAttorney;
      },

      // Update attorney
      async updateAttorney(updates) {
        if (!this.currentAttorney) {
          throw new Error('No attorney to update');
        }

        const updatedAttorney = {
          ...this.currentAttorney,
          ...updates,
          updated_at: new Date().toISOString()
        };

        this.currentAttorney = updatedAttorney;
        this.saveToLocalStorage(updatedAttorney);
        this.notifyListeners();

        return updatedAttorney;
      }
    };
  };

  // Get current subdomain
  const getCurrentSubdomain = () => {
    if (typeof window === 'undefined') return 'default';

    const host = window.location.hostname;

    // Remove localhost special case - treat localhost like production

    // Extract subdomain from hostname
    const parts = host.split('.');

    // Check if we have a subdomain (e.g., damon.legalscout.net)
    if (parts.length > 2 && parts[0] !== 'www') {
      return parts[0];
    }

    return 'default';
  };

  // Initialize the unified attorney manager using GlobalAttorneyCoordinator
  const initializeUnifiedManager = async () => {
    try {
      console.log('[UnifiedAttorneyManager] Creating unified attorney manager...');

      // Wait for coordinator
      const coordinator = await waitForCoordinator();
      if (!coordinator) {
        throw new Error('GlobalAttorneyCoordinator not available');
      }

      // Determine context type
      const contextType = determineContextType();
      const contextId = generateContextId(contextType);

      console.log(`[UnifiedAttorneyManager] Registering as ${contextType} context: ${contextId}`);

      // Register with coordinator
      await coordinator.registerContext(contextId, contextType, {
        canUpdate: true,
        canSync: true,
        realTimeUpdates: true
      });

      // Initialize attorney state
      const attorney = await coordinator.initialize(contextId);

      // Create compatibility layer for existing code
      const unifiedManager = {
        attorney: attorney,
        isInitialized: coordinator.isInitialized(),
        contextId: contextId,
        coordinator: coordinator,

        // Compatibility methods
        getAttorney: () => coordinator.getAttorney(),
        updateAttorney: (updates) => coordinator.updateAttorney(updates, contextId),

        // Event handling
        subscribe: (callback) => coordinator.on('stateChange', (data) => callback(data.attorney)),
        unsubscribe: (callback) => {
          // Note: This is simplified - in production you'd want proper unsubscribe
          console.warn('[UnifiedAttorneyManager] Unsubscribe not fully implemented');
        },

        // Heartbeat to keep context alive
        updateHeartbeat: () => coordinator.updateHeartbeat(contextId),

        // Legacy compatibility
        loadFromLocalStorage: () => coordinator.getAttorney(),
        saveToLocalStorage: (attorney) => coordinator.updateAttorney(attorney, contextId),
        notifySubscribers: () => {
          // Coordinator handles this automatically
        }
      };

      // Set up heartbeat
      setInterval(() => {
        unifiedManager.updateHeartbeat();
      }, 5000);

      // Replace global managers
      window.attorneyProfileManager = unifiedManager;
      window.standaloneAttorneyManager = unifiedManager;

      // Disable old StandaloneAttorneyManager initialization
      window.DISABLE_STANDALONE_ATTORNEY_MANAGER = true;

      console.log('[UnifiedAttorneyManager] Unified attorney manager initialized successfully');

      if (attorney) {
        console.log('[UnifiedAttorneyManager] Attorney loaded:', {
          id: attorney.id,
          subdomain: attorney.subdomain,
          firm_name: attorney.firm_name,
          contextType: contextType
        });
      }

      return unifiedManager;

    } catch (error) {
      console.error('[UnifiedAttorneyManager] Error initializing unified manager:', error);
      throw error;
    }
  };

  // Determine context type based on current environment
  const determineContextType = () => {
    const path = window.location.pathname;
    const hostname = window.location.hostname;

    // Check if we're in dashboard
    if (path.includes('/dashboard') || hostname.includes('dashboard')) {
      return 'dashboard';
    }

    // Check if we're in preview
    if (path.includes('/simple-preview') || path.includes('/preview')) {
      return 'preview';
    }

    // Check if we're on a subdomain
    const subdomain = getCurrentSubdomain();
    if (subdomain && subdomain !== 'default' && subdomain !== 'www') {
      return 'subdomain';
    }

    // Default to dashboard
    return 'dashboard';
  };

  // Generate unique context ID
  const generateContextId = (contextType) => {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substr(2, 9);
    const subdomain = getCurrentSubdomain();

    if (contextType === 'subdomain' && subdomain !== 'default') {
      return `${contextType}-${subdomain}-${timestamp}-${random}`;
    }

    return `${contextType}-${timestamp}-${random}`;
  };

  // Wait for dependencies and initialize
  const waitForDependencies = () => {
    let attempts = 0;
    const maxAttempts = 100; // 10 seconds max wait

    const checkDependencies = () => {
      attempts++;

      // Check if required dependencies are available
      const hasSupabase = window.supabase;
      const hasCoordinator = window.GlobalAttorneyCoordinator;

      if ((hasSupabase && hasCoordinator) || attempts >= maxAttempts) {
        console.log('[UnifiedAttorneyManager] Dependencies ready, initializing...');
        console.log(`[UnifiedAttorneyManager] Supabase: ${!!hasSupabase}, Coordinator: ${!!hasCoordinator}`);
        initializeUnifiedManager().catch(error => {
          console.error('[UnifiedAttorneyManager] Initialization failed:', error);
        });
      } else {
        setTimeout(checkDependencies, 100);
      }
    };

    checkDependencies();
  };

  // Start the initialization process
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', waitForDependencies);
  } else {
    waitForDependencies();
  }

})();
