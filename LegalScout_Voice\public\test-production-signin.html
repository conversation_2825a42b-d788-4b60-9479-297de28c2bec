<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Production Sign-In Fixes</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-result {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>🧪 Production Sign-In Fixes Test</h1>
    
    <div class="test-container">
        <h2>Environment Variables Test</h2>
        <button onclick="testEnvironmentVariables()">Test Environment Variables</button>
        <div id="env-results"></div>
    </div>

    <div class="test-container">
        <h2>Supabase Connection Test</h2>
        <button onclick="testSupabaseConnection()">Test Supabase Connection</button>
        <div id="supabase-results"></div>
    </div>

    <div class="test-container">
        <h2>Vapi MCP Fallback Test</h2>
        <button onclick="testVapiMcpFallback()">Test Vapi MCP Fallback</button>
        <div id="vapi-results"></div>
    </div>

    <div class="test-container">
        <h2>MutationObserver Test</h2>
        <button onclick="testMutationObserver()">Test MutationObserver</button>
        <div id="observer-results"></div>
    </div>

    <div class="test-container">
        <h2>Console Log</h2>
        <div id="console-log" class="log"></div>
        <button onclick="clearLog()">Clear Log</button>
    </div>

    <!-- Load the production sign-in fix -->
    <script src="/production-signin-fix.js"></script>

    <script>
        // Capture console logs
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        function addToLog(type, message) {
            const logDiv = document.getElementById('console-log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.textContent += `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToLog('log', args.join(' '));
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToLog('error', args.join(' '));
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToLog('warn', args.join(' '));
        };

        function clearLog() {
            document.getElementById('console-log').textContent = '';
        }

        function showResult(containerId, success, message) {
            const container = document.getElementById(containerId);
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${success ? 'success' : 'error'}`;
            resultDiv.textContent = `${success ? '✅' : '❌'} ${message}`;
            container.appendChild(resultDiv);
        }

        function testEnvironmentVariables() {
            const container = document.getElementById('env-results');
            container.innerHTML = '';
            
            console.log('Testing environment variables...');
            
            // Test window variables
            const windowVars = [
                'VITE_SUPABASE_URL',
                'VITE_SUPABASE_KEY',
                'VITE_VAPI_PUBLIC_KEY'
            ];
            
            windowVars.forEach(varName => {
                const exists = window[varName] !== undefined;
                showResult('env-results', exists, `window.${varName}: ${exists ? 'Set' : 'Not set'}`);
            });
            
            // Test import.meta.env
            if (window.import && window.import.meta && window.import.meta.env) {
                const importMetaVars = [
                    'VITE_SUPABASE_URL',
                    'VITE_SUPABASE_KEY',
                    'VITE_VAPI_PUBLIC_KEY'
                ];
                
                importMetaVars.forEach(varName => {
                    const exists = window.import.meta.env[varName] !== undefined;
                    showResult('env-results', exists, `import.meta.env.${varName}: ${exists ? 'Set' : 'Not set'}`);
                });
            } else {
                showResult('env-results', false, 'import.meta.env not available');
            }
        }

        async function testSupabaseConnection() {
            const container = document.getElementById('supabase-results');
            container.innerHTML = '';
            
            console.log('Testing Supabase connection...');
            
            try {
                // Test if we can make a basic request to Supabase
                const response = await fetch('https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/', {
                    method: 'GET',
                    headers: {
                        'apikey': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzg5NDgwMDcsImV4cCI6MjA1NDUyNDAwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU',
                        'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzg5NDgwMDcsImV4cCI6MjA1NDUyNDAwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU',
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    showResult('supabase-results', true, `Supabase connection successful (${response.status})`);
                } else {
                    showResult('supabase-results', false, `Supabase connection failed (${response.status})`);
                }
            } catch (error) {
                showResult('supabase-results', false, `Supabase connection error: ${error.message}`);
            }
        }

        async function testVapiMcpFallback() {
            const container = document.getElementById('vapi-results');
            container.innerHTML = '';
            
            console.log('Testing Vapi MCP fallback...');
            
            try {
                // This should trigger the CORS fallback
                const response = await fetch('https://mcp.vapi.ai/mcp', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ test: true })
                });
                
                showResult('vapi-results', false, 'Vapi MCP request should have been blocked');
            } catch (error) {
                if (error.message.includes('CORS blocked') || error.message.includes('falling back')) {
                    showResult('vapi-results', true, 'Vapi MCP CORS fallback working correctly');
                } else {
                    showResult('vapi-results', true, `Vapi MCP blocked as expected: ${error.message}`);
                }
            }
        }

        function testMutationObserver() {
            const container = document.getElementById('observer-results');
            container.innerHTML = '';
            
            console.log('Testing MutationObserver...');
            
            try {
                const observer = new MutationObserver(() => {});
                
                // Test with valid node
                observer.observe(document.body, { childList: true });
                showResult('observer-results', true, 'MutationObserver works with valid node');
                
                // Test with invalid node
                try {
                    observer.observe(null, { childList: true });
                    showResult('observer-results', false, 'MutationObserver should have failed with null');
                } catch (error) {
                    showResult('observer-results', true, 'MutationObserver correctly handles invalid node');
                }
                
                observer.disconnect();
            } catch (error) {
                showResult('observer-results', false, `MutationObserver test failed: ${error.message}`);
            }
        }

        // Run initial tests
        window.addEventListener('load', () => {
            console.log('Page loaded, production sign-in fixes should be active');
            addToLog('info', 'Test page loaded successfully');
        });
    </script>
</body>
</html>
