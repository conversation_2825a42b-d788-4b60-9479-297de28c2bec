/**
 * Supabase Realtime Sync
 * 
 * This script sets up Supabase Realtime subscriptions to keep attorney data
 * in sync across the application and with the database.
 */

(function() {
  console.log('[SupabaseRealtimeSync] Initializing...');
  
  // Wait for Supabase client to be available
  const checkInterval = setInterval(() => {
    if (window.supabase) {
      clearInterval(checkInterval);
      initializeRealtimeSync();
    }
  }, 100);
  
  // Clear interval after 10 seconds if Supabase is not available
  setTimeout(() => {
    clearInterval(checkInterval);
    console.warn('[SupabaseRealtimeSync] Timed out waiting for Supabase client');
  }, 10000);
  
  /**
   * Initialize Realtime sync
   */
  function initializeRealtimeSync() {
    console.log('[SupabaseRealtimeSync] Setting up realtime subscriptions...');
    
    try {
      // Get the current attorney ID from localStorage
      const attorneyId = localStorage.getItem('attorney_id');
      
      if (!attorneyId) {
        console.warn('[SupabaseRealtimeSync] No attorney ID found in localStorage');
        return;
      }
      
      console.log('[SupabaseRealtimeSync] Setting up subscription for attorney:', attorneyId);
      
      // Subscribe to changes on the attorneys table for this specific attorney
      const subscription = window.supabase
        .channel('attorney-changes')
        .on('postgres_changes', {
          event: '*',
          schema: 'public',
          table: 'attorneys',
          filter: `id=eq.${attorneyId}`
        }, handleAttorneyChange)
        .subscribe();
      
      console.log('[SupabaseRealtimeSync] Subscription set up successfully');
      
      // Store the subscription for cleanup
      window.supabaseSubscription = subscription;
      
      // Initial fetch to ensure we have the latest data
      fetchCurrentAttorney(attorneyId);
    } catch (error) {
      console.error('[SupabaseRealtimeSync] Error setting up realtime subscription:', error);
    }
  }
  
  /**
   * Handle attorney change event from Supabase Realtime
   * @param {Object} payload - The change payload
   */
  function handleAttorneyChange(payload) {
    console.log('[SupabaseRealtimeSync] Received attorney change:', payload);
    
    try {
      // Get the updated attorney data
      const updatedAttorney = payload.new;
      
      if (!updatedAttorney) {
        console.warn('[SupabaseRealtimeSync] No attorney data in payload');
        return;
      }
      
      // Update localStorage
      localStorage.setItem('attorney', JSON.stringify(updatedAttorney));
      localStorage.setItem('attorney_id', updatedAttorney.id);
      localStorage.setItem('attorney_version', Date.now().toString());
      
      console.log('[SupabaseRealtimeSync] Updated attorney in localStorage:', updatedAttorney.id);
      
      // Update standalone attorney manager if available
      if (window.standaloneAttorneyManager) {
        window.standaloneAttorneyManager.attorney = updatedAttorney;
        window.standaloneAttorneyManager.notifySubscribers();
        console.log('[SupabaseRealtimeSync] Updated standalone attorney manager');
      }
      
      // Update attorney state manager if available
      if (window.attorneyStateManager) {
        window.attorneyStateManager.attorney = updatedAttorney;
        window.attorneyStateManager.notifySubscribers();
        console.log('[SupabaseRealtimeSync] Updated attorney state manager');
      }
    } catch (error) {
      console.error('[SupabaseRealtimeSync] Error handling attorney change:', error);
    }
  }
  
  /**
   * Fetch the current attorney from Supabase
   * @param {string} attorneyId - The attorney ID to fetch
   */
  async function fetchCurrentAttorney(attorneyId) {
    console.log('[SupabaseRealtimeSync] Fetching current attorney data...');
    
    try {
      const { data, error } = await window.supabase
        .from('attorneys')
        .select('*')
        .eq('id', attorneyId)
        .single();
      
      if (error) {
        throw error;
      }
      
      if (data) {
        console.log('[SupabaseRealtimeSync] Fetched attorney data:', data);
        
        // Update localStorage
        localStorage.setItem('attorney', JSON.stringify(data));
        localStorage.setItem('attorney_id', data.id);
        localStorage.setItem('attorney_version', Date.now().toString());
        
        // Update standalone attorney manager if available
        if (window.standaloneAttorneyManager) {
          window.standaloneAttorneyManager.attorney = data;
          window.standaloneAttorneyManager.notifySubscribers();
        }
        
        // Update attorney state manager if available
        if (window.attorneyStateManager) {
          window.attorneyStateManager.attorney = data;
          window.attorneyStateManager.notifySubscribers();
        }
      } else {
        console.warn('[SupabaseRealtimeSync] No attorney data found in Supabase');
      }
    } catch (error) {
      console.error('[SupabaseRealtimeSync] Error fetching attorney data:', error);
    }
  }
  
  // Expose cleanup function
  window.cleanupSupabaseRealtimeSync = function() {
    if (window.supabaseSubscription) {
      window.supabaseSubscription.unsubscribe();
      console.log('[SupabaseRealtimeSync] Subscription cleaned up');
    }
  };
  
  console.log('[SupabaseRealtimeSync] Initialization complete');
})();
