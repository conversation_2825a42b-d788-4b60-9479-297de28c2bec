/**
 * FINAL SUPABASE FIX
 * 
 * This is the definitive fix for Supabase API key preservation.
 * It runs LAST and ensures all Supabase requests have proper authentication.
 */

(function() {
  // Check if disabled by clean auth solution
  if (window.__FINAL_SUPABASE_FIX_DISABLED) {
    console.log('[FinalSupabaseFix] 🚫 Disabled by clean auth solution');
    return;
  }

  console.log('[FinalSupabaseFix] 🎯 Applying definitive Supabase API key fix...');

  // Wait for all other scripts to load, then apply our fix
  setTimeout(() => {
    console.log('[FinalSupabaseFix] 🔧 Applying final fetch interceptor...');
    
    // Store the current fetch (which may have been modified by other scripts)
    const currentFetch = window.fetch;
    
    // Create our definitive fetch interceptor
    window.fetch = function(url, options = {}) {
      // Only intercept Supabase requests
      if (typeof url === 'string' && url.includes('supabase.co')) {
        console.log('[FinalSupabaseFix] 📊 Ensuring Supabase API key is preserved');
        
        // Ensure options object exists
        options = options || {};
        options.headers = options.headers || {};
        
        // Convert Headers object to plain object if needed
        if (options.headers instanceof Headers) {
          const headersObj = {};
          for (const [key, value] of options.headers.entries()) {
            headersObj[key] = value;
          }
          options.headers = headersObj;
        }
        
        // Ensure Supabase API key is present
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzg5NDgwMDcsImV4cCI6MjA1NDUyNDAwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU';
        
        // Force the API key headers (override any previous modifications)
        options.headers['apikey'] = supabaseKey;
        options.headers['Authorization'] = `Bearer ${supabaseKey}`;
        
        // Preserve other important headers
        if (!options.headers['Content-Type']) {
          options.headers['Content-Type'] = 'application/json';
        }
        if (!options.headers['Accept']) {
          options.headers['Accept'] = 'application/json';
        }
        
        console.log('[FinalSupabaseFix] ✅ Supabase API key headers ensured');
      }
      
      // Call the current fetch (which may be from other interceptors)
      return currentFetch.call(this, url, options);
    };
    
    console.log('[FinalSupabaseFix] ✅ Final fetch interceptor applied successfully');
    
    // Mark as applied
    window.__FINAL_SUPABASE_FIX_APPLIED = true;
    
  }, 1000); // Wait 1 second for all other scripts to load
  
})();
