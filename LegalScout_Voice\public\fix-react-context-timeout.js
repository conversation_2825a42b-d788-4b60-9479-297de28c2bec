/**
 * Fix React Context Timeout
 *
 * This script provides a fix for the React context timeout issue
 * by creating fallback contexts when needed.
 *
 * Updated to fix the "Maximum update depth exceeded" error.
 */

(function() {
  console.log('[FixReactContextTimeout] Starting fix...');

  // Global error handler for React context errors
  window.addEventListener('error', function(event) {
    if (event.error && event.error.message && (
      event.error.message.includes('Maximum update depth exceeded') ||
      event.error.message.includes('Too many re-renders') ||
      event.error.message.includes('Rendered fewer hooks than expected') ||
      event.error.message.includes('Context Provider')
    )) {
      console.log('[FixReactContextTimeout] Caught React context error: ' + event.error.message);
      event.preventDefault();
      return false;
    }
  }, true);

  // Add a global handler for unhandled promise rejections
  window.addEventListener('unhandledrejection', function(event) {
    if (event.reason && event.reason.message && (
      event.reason.message.includes('Maximum update depth exceeded') ||
      event.reason.message.includes('Too many re-renders') ||
      event.reason.message.includes('Rendered fewer hooks than expected') ||
      event.reason.message.includes('Context Provider')
    )) {
      console.log('[FixReactContextTimeout] Caught unhandled React context rejection:', event.reason);
      event.preventDefault();
    }
  });

  // Check if React is available
  if (!window.React) {
    console.log('[FixReactContextTimeout] Creating global React object');
    window.React = {
      createContext: function(defaultValue) {
        console.log('[FixReactContextTimeout] Creating context with default value:', defaultValue);

        // Create a minimal context object
        const context = {
          Provider: function({ value, children }) {
            return children;
          },
          Consumer: function({ children }) {
            return children(defaultValue);
          },
          displayName: 'FixedContext',
          _currentValue: defaultValue,
          _currentValue2: defaultValue
        };

        return context;
      }
    };
  } else {
    // Patch React.createContext if it exists
    if (typeof window.React.createContext === 'function' && !window.React.createContext._reactContextFixPatched) {
      console.log('[FixReactContextTimeout] Patching React.createContext');

      // Store the original createContext function
      const originalCreateContext = window.React.createContext;

      // Create a wrapped version of createContext
      window.React.createContext = function(defaultValue, calculateChangedBits) {
        try {
          // Call the original createContext function
          const context = originalCreateContext(defaultValue, calculateChangedBits);

          // Store the original Provider component
          const originalProvider = context.Provider;

          // Create a wrapped version of the Provider component
          context.Provider = function(props) {
            try {
              // Call the original Provider component
              return originalProvider(props);
            } catch (error) {
              console.error('[FixReactContextTimeout] Error in Provider component:', error);
              // Return the children directly as a fallback
              return props.children || null;
            }
          };

          // Copy properties from the original Provider
          Object.assign(context.Provider, originalProvider);
          context.Provider.displayName = originalProvider.displayName || 'Provider';

          // Store the original Consumer component
          const originalConsumer = context.Consumer;

          // Create a wrapped version of the Consumer component
          context.Consumer = function(props) {
            try {
              // Call the original Consumer component
              return originalConsumer(props);
            } catch (error) {
              console.error('[FixReactContextTimeout] Error in Consumer component:', error);
              // Return the default value as a fallback
              return props.children ? props.children(defaultValue) : null;
            }
          };

          // Copy properties from the original Consumer
          Object.assign(context.Consumer, originalConsumer);
          context.Consumer.displayName = originalConsumer.displayName || 'Consumer';

          return context;
        } catch (error) {
          console.error('[FixReactContextTimeout] Error in createContext:', error);
          // Return a minimal context implementation
          return {
            Provider: function(props) { return props.children || null; },
            Consumer: function(props) { return props.children ? props.children(defaultValue) : null; },
            displayName: 'FallbackContext',
            _currentValue: defaultValue,
            _currentValue2: defaultValue,
            _threadCount: 0,
            _defaultValue: defaultValue
          };
        }
      };

      // Copy properties from the original createContext function
      Object.assign(window.React.createContext, originalCreateContext);
      window.React.createContext._reactContextFixPatched = true;

      console.log('[FixReactContextTimeout] React.createContext patched successfully');
    }
  }

  // Create fallback for AttorneyStateContext
  if (!window.AttorneyStateContext) {
    console.log('[FixReactContextTimeout] Creating fallback AttorneyStateContext');

    // Create a default attorney state
    const defaultAttorneyState = {
      attorney: window.standaloneAttorneyManager ? window.standaloneAttorneyManager.attorney : null,
      isLoading: false,
      isSaving: false,
      isSyncing: false,
      error: null,
      updateAttorney: function(updates) {
        console.log('[FixReactContextTimeout] updateAttorney called with fallback');
        if (window.standaloneAttorneyManager) {
          return window.standaloneAttorneyManager.updateAttorney(updates);
        }
        return null;
      },
      createAttorney: function(attorneyData) {
        console.log('[FixReactContextTimeout] createAttorney called with fallback');
        if (window.standaloneAttorneyManager) {
          return window.standaloneAttorneyManager.updateAttorney(attorneyData);
        }
        return null;
      },
      syncWithVapi: function(options) {
        console.log('[FixReactContextTimeout] syncWithVapi called with fallback');
        if (window.standaloneAttorneyManager) {
          return window.standaloneAttorneyManager.syncWithVapi();
        }
        return Promise.resolve({ action: 'none' });
      }
    };

    // Create the context
    window.AttorneyStateContext = window.React.createContext(defaultAttorneyState);
    window.AttorneyStateContext.displayName = 'AttorneyStateContext';

    console.log('[FixReactContextTimeout] AttorneyStateContext fallback created');
  }

  // Create fallback for AuthContext
  if (!window.AuthContext) {
    console.log('[FixReactContextTimeout] Creating fallback AuthContext');

    // Create a default auth state
    const defaultAuthState = {
      user: null,
      session: null,
      isLoading: false,
      signIn: function() {
        console.log('[FixReactContextTimeout] signIn called with fallback');
        return Promise.resolve(null);
      },
      signOut: function() {
        console.log('[FixReactContextTimeout] signOut called with fallback');
        return Promise.resolve();
      },
      signUp: function() {
        console.log('[FixReactContextTimeout] signUp called with fallback');
        return Promise.resolve(null);
      }
    };

    // Create the context
    window.AuthContext = window.React.createContext(defaultAuthState);
    window.AuthContext.displayName = 'AuthContext';

    console.log('[FixReactContextTimeout] AuthContext fallback created');
  }

  console.log('[FixReactContextTimeout] Fix completed');
})();
