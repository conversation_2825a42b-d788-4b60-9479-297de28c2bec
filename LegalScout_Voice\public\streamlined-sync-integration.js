/**
 * ULTRA-THINKING Streamlined Sync Integration
 * 
 * This script replaces all the complex, overlapping sync systems with a single,
 * efficient integration that leverages the optimized sync engine.
 * 
 * Key Benefits:
 * 1. Eliminates redundant systems
 * 2. Uses efficient Vercel-Supabase integration
 * 3. Leverages Vapi MCP Server with streamable-HTTP
 * 4. Real-time sync with minimal overhead
 * 5. Automatic conflict resolution
 */

(function() {
  'use strict';

  console.log('[StreamlinedSyncIntegration] 🚀 Initializing streamlined sync integration...');

  // Configuration
  const config = {
    syncInterval: 30000, // 30 seconds
    retryAttempts: 3,
    retryDelay: 1000, // 1 second
    cacheTimeout: 60000 // 1 minute
  };

  // State management
  let syncService = null;
  let isInitialized = false;
  let currentUser = null;

  /**
   * Initialize the streamlined sync integration
   */
  async function initialize() {
    if (isInitialized) return;

    try {
      console.log('[StreamlinedSyncIntegration] 🔧 Setting up streamlined sync...');

      // Wait for required services to be available
      await waitForServices();

      // Import and initialize the streamlined sync service
      if (window.streamlinedSyncService) {
        syncService = window.streamlinedSyncService;
      } else {
        // Fallback: create a minimal sync service
        syncService = createMinimalSyncService();
      }

      await syncService.initialize();

      // Set up auth state monitoring
      setupAuthStateMonitoring();

      // Set up automatic conflict resolution
      setupConflictResolution();

      isInitialized = true;
      console.log('[StreamlinedSyncIntegration] ✅ Streamlined sync integration ready');

    } catch (error) {
      console.error('[StreamlinedSyncIntegration] ❌ Initialization failed:', error);
    }
  }

  /**
   * Wait for required services to be available
   */
  async function waitForServices() {
    const maxWait = 10000; // 10 seconds
    const checkInterval = 100; // 100ms
    let waited = 0;

    while (waited < maxWait) {
      if (window.supabase && (window.streamlinedSyncService || window.fetch)) {
        console.log('[StreamlinedSyncIntegration] ✅ Required services available');
        return;
      }

      await new Promise(resolve => setTimeout(resolve, checkInterval));
      waited += checkInterval;
    }

    throw new Error('Required services not available after timeout');
  }

  /**
   * Set up auth state monitoring
   */
  function setupAuthStateMonitoring() {
    if (!window.supabase) return;

    // Listen for auth state changes
    window.supabase.auth.onAuthStateChange(async (event, session) => {
      console.log(`[StreamlinedSyncIntegration] 🔐 Auth state changed: ${event}`);

      if (event === 'SIGNED_IN' && session?.user) {
        currentUser = session.user;
        await handleUserSignIn(session.user);
      } else if (event === 'SIGNED_OUT') {
        currentUser = null;
        handleUserSignOut();
      }
    });

    console.log('[StreamlinedSyncIntegration] ✅ Auth state monitoring active');
  }

  /**
   * Handle user sign in
   */
  async function handleUserSignIn(user) {
    try {
      console.log(`[StreamlinedSyncIntegration] 👤 User signed in: ${user.email}`);

      // Sync attorney data
      const syncResult = await syncService.syncAttorney(user.email, user.id);
      
      // Check for conflicts and auto-fix if needed
      if (!syncResult.assistantValid && syncResult.assistantMappings.length > 0) {
        console.log('[StreamlinedSyncIntegration] 🔧 Detected assistant conflict, auto-fixing...');
        await syncService.fixConflicts(user.email);
      }

      // Dispatch success event
      window.dispatchEvent(new CustomEvent('streamlinedSyncComplete', {
        detail: { user, syncResult }
      }));

    } catch (error) {
      console.error('[StreamlinedSyncIntegration] ❌ User sign in sync failed:', error);
      
      // Dispatch error event
      window.dispatchEvent(new CustomEvent('streamlinedSyncError', {
        detail: { user, error: error.message }
      }));
    }
  }

  /**
   * Handle user sign out
   */
  function handleUserSignOut() {
    console.log('[StreamlinedSyncIntegration] 👋 User signed out');
    
    // Clear any cached data
    if (syncService && syncService.cache) {
      syncService.cache.clear();
    }
  }

  /**
   * Set up automatic conflict resolution
   */
  function setupConflictResolution() {
    // Listen for assistant reconciliation events
    window.addEventListener('assistantReconciled', async (event) => {
      console.log('[StreamlinedSyncIntegration] 🤖 Assistant reconciled:', event.detail);
      
      if (currentUser) {
        // Re-sync after reconciliation
        try {
          await syncService.syncAttorney(currentUser.email, currentUser.id);
        } catch (error) {
          console.error('[StreamlinedSyncIntegration] ❌ Post-reconciliation sync failed:', error);
        }
      }
    });

    // Listen for real-time sync updates
    window.addEventListener('streamlinedSyncUpdate', (event) => {
      console.log('[StreamlinedSyncIntegration] 📡 Real-time sync update:', event.detail);
      
      // Dispatch to other components that might need to update
      window.dispatchEvent(new CustomEvent('attorneyDataUpdated', {
        detail: event.detail
      }));
    });

    console.log('[StreamlinedSyncIntegration] ✅ Conflict resolution active');
  }

  /**
   * Create minimal sync service as fallback
   */
  function createMinimalSyncService() {
    return {
      cache: new Map(),
      
      async initialize() {
        console.log('[StreamlinedSyncIntegration] 📦 Using minimal sync service');
      },

      async syncAttorney(email, userId) {
        const response = await fetch('/api/optimized-sync', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            action: 'sync_attorney',
            data: { email, userId }
          })
        });

        if (!response.ok) {
          throw new Error(`Sync failed: ${response.status}`);
        }

        const result = await response.json();
        if (!result.success) {
          throw new Error(result.error);
        }

        return result.data;
      },

      async fixConflicts(email) {
        const response = await fetch('/api/optimized-sync', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            action: 'fix_conflicts',
            data: { email }
          })
        });

        if (!response.ok) {
          throw new Error(`Fix conflicts failed: ${response.status}`);
        }

        const result = await response.json();
        if (!result.success) {
          throw new Error(result.error);
        }

        return result.data;
      }
    };
  }

  /**
   * Public API for manual operations
   */
  window.StreamlinedSync = {
    async syncCurrentUser() {
      if (!currentUser) {
        throw new Error('No user signed in');
      }
      return await syncService.syncAttorney(currentUser.email, currentUser.id);
    },

    async fixConflicts() {
      if (!currentUser) {
        throw new Error('No user signed in');
      }
      return await syncService.fixConflicts(currentUser.email);
    },

    async getStatus() {
      return await syncService.getSyncStatus();
    },

    getMetrics() {
      return syncService.getMetrics ? syncService.getMetrics() : {};
    }
  };

  /**
   * Initialize when DOM is ready
   */
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initialize);
  } else {
    // DOM is already ready
    setTimeout(initialize, 100);
  }

  console.log('[StreamlinedSyncIntegration] 📋 Streamlined sync integration script loaded');

})();
