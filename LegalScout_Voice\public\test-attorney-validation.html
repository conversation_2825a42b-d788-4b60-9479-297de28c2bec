<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Test Attorney Validation</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 20px;
      line-height: 1.6;
    }
    h1 {
      color: #333;
    }
    .container {
      max-width: 800px;
      margin: 0 auto;
    }
    .log-container {
      background-color: #f5f5f5;
      border: 1px solid #ddd;
      padding: 10px;
      border-radius: 4px;
      height: 400px;
      overflow-y: auto;
      margin-bottom: 20px;
      font-family: monospace;
    }
    .log-entry {
      margin: 5px 0;
      padding: 5px;
      border-bottom: 1px solid #eee;
    }
    .log-entry.error {
      color: #d9534f;
    }
    .log-entry.warn {
      color: #f0ad4e;
    }
    .log-entry.info {
      color: #5bc0de;
    }
    .log-entry.success {
      color: #5cb85c;
    }
    button {
      padding: 10px 15px;
      background-color: #007bff;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      margin-right: 10px;
    }
    button:hover {
      background-color: #0069d9;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Test Attorney Validation</h1>
    <div class="log-container" id="logContainer"></div>
    <div>
      <button id="clearLog">Clear Log</button>
      <button id="runTests">Run Tests</button>
    </div>
  </div>

  <script>
    // Override console methods to display in the log container
    const logContainer = document.getElementById('logContainer');
    
    const originalConsole = {
      log: console.log,
      error: console.error,
      warn: console.warn,
      info: console.info
    };
    
    console.log = function(...args) {
      originalConsole.log.apply(console, args);
      const logEntry = document.createElement('div');
      logEntry.className = 'log-entry';
      logEntry.textContent = args.map(arg => typeof arg === 'object' ? JSON.stringify(arg) : arg).join(' ');
      logContainer.appendChild(logEntry);
      logContainer.scrollTop = logContainer.scrollHeight;
    };
    
    console.error = function(...args) {
      originalConsole.error.apply(console, args);
      const logEntry = document.createElement('div');
      logEntry.className = 'log-entry error';
      logEntry.textContent = args.map(arg => typeof arg === 'object' ? JSON.stringify(arg) : arg).join(' ');
      logContainer.appendChild(logEntry);
      logContainer.scrollTop = logContainer.scrollHeight;
    };
    
    console.warn = function(...args) {
      originalConsole.warn.apply(console, args);
      const logEntry = document.createElement('div');
      logEntry.className = 'log-entry warn';
      logEntry.textContent = args.map(arg => typeof arg === 'object' ? JSON.stringify(arg) : arg).join(' ');
      logContainer.appendChild(logEntry);
      logContainer.scrollTop = logContainer.scrollHeight;
    };
    
    console.info = function(...args) {
      originalConsole.info.apply(console, args);
      const logEntry = document.createElement('div');
      logEntry.className = 'log-entry info';
      logEntry.textContent = args.map(arg => typeof arg === 'object' ? JSON.stringify(arg) : arg).join(' ');
      logContainer.appendChild(logEntry);
      logContainer.scrollTop = logContainer.scrollHeight;
    };
    
    // Clear log button
    document.getElementById('clearLog').addEventListener('click', () => {
      logContainer.innerHTML = '';
    });
    
    // Run tests button
    document.getElementById('runTests').addEventListener('click', () => {
      loadScript('/test-attorney-validation.js');
    });
    
    // Load scripts
    function loadScript(src) {
      const script = document.createElement('script');
      script.src = src;
      script.onload = () => {
        console.log(`Script loaded: ${src}`);
      };
      script.onerror = (error) => {
        console.error(`Error loading script ${src}:`, error);
      };
      document.body.appendChild(script);
    }
    
    // Load the standalone attorney manager
    loadScript('/standalone-attorney-manager-fixed.js');
  </script>
</body>
</html>
