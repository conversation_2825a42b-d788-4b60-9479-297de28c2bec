/**
 * Unified Vapi Integration Service
 * 
 * Implements the dual transport pattern recommended in the new guidelines:
 * - SSE (Server-Sent Events) for real-time operations
 * - Streamable HTTP for administrative operations
 * 
 * Based on latest Vapi documentation:
 * - https://docs.vapi.ai/sdk/web
 * - https://docs.vapi.ai/sdk/mcp-server
 */

import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { SSEClientTransport } from '@modelcontextprotocol/sdk/client/sse.js';
import { StreamableHTTPClientTransport } from '@modelcontextprotocol/sdk/client/streamableHttp.js';
import Vapi from '@vapi-ai/web';

class VapiIntegrationService {
  constructor() {
    // Transport clients
    this.sseClient = null;
    this.httpClient = null;
    this.webClient = null;
    
    // Connection states
    this.sseConnected = false;
    this.httpConnected = false;
    this.webConnected = false;
    
    // Configuration
    this.apiKey = null;
    this.publicKey = null;
    
    // URLs
    this.mcpSSEUrl = 'https://mcp.vapi.ai/sse';
    this.mcpHTTPUrl = 'https://mcp.vapi.ai/mcp';
    
    // Error handling
    this.maxRetries = 3;
    this.retryDelay = 1000;
  }

  /**
   * Initialize the service with API keys
   * @param {string} publicKey - Vapi public API key for client-side operations
   * @param {string} privateKey - Vapi private API key for server-side operations
   */
  async initialize(publicKey, privateKey) {
    this.publicKey = publicKey;
    this.apiKey = privateKey;
    
    console.log('[VapiIntegrationService] Initializing with dual transport pattern');
    
    // Initialize Web SDK for client-side voice calls
    await this.initializeWebClient();
    
    // Initialize MCP clients for administrative operations
    await this.initializeHTTPClient();
    await this.initializeSSEClient();
    
    return {
      web: this.webConnected,
      http: this.httpConnected,
      sse: this.sseConnected
    };
  }

  /**
   * Initialize Vapi Web SDK for voice calls
   */
  async initializeWebClient() {
    try {
      if (!this.publicKey) {
        throw new Error('Public API key required for Web SDK');
      }
      
      this.webClient = new Vapi(this.publicKey);
      this.webConnected = true;
      
      console.log('[VapiIntegrationService] ✅ Web SDK initialized');
      return true;
    } catch (error) {
      console.error('[VapiIntegrationService] ❌ Web SDK initialization failed:', error);
      this.webConnected = false;
      return false;
    }
  }

  /**
   * Initialize Streamable HTTP client for administrative operations
   */
  async initializeHTTPClient() {
    try {
      if (!this.apiKey) {
        throw new Error('Private API key required for HTTP client');
      }
      
      this.httpClient = new Client({
        name: 'legalscout-http-client',
        version: '1.0.0'
      });
      
      const transport = new StreamableHTTPClientTransport(
        new URL(this.mcpHTTPUrl),
        {
          requestInit: {
            headers: {
              'Authorization': `Bearer ${this.apiKey}`
            }
          }
        }
      );
      
      await this.httpClient.connect(transport);
      this.httpConnected = true;
      
      console.log('[VapiIntegrationService] ✅ HTTP client connected');
      return true;
    } catch (error) {
      console.error('[VapiIntegrationService] ❌ HTTP client connection failed:', error);
      this.httpConnected = false;
      return false;
    }
  }

  /**
   * Initialize SSE client for real-time operations
   */
  async initializeSSEClient() {
    try {
      if (!this.apiKey) {
        throw new Error('Private API key required for SSE client');
      }
      
      this.sseClient = new Client({
        name: 'legalscout-sse-client',
        version: '1.0.0'
      });
      
      const transport = new SSEClientTransport({
        url: this.mcpSSEUrl,
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
          'Accept': 'text/event-stream',
          'Cache-Control': 'no-cache'
        }
      });
      
      await this.sseClient.connect(transport);
      this.sseConnected = true;
      
      console.log('[VapiIntegrationService] ✅ SSE client connected');
      return true;
    } catch (error) {
      console.error('[VapiIntegrationService] ❌ SSE client connection failed:', error);
      this.sseConnected = false;
      return false;
    }
  }

  // === VOICE CALL OPERATIONS (Web SDK) ===

  /**
   * Start a voice call using the Web SDK
   * @param {string} assistantId - Assistant ID to use for the call
   * @param {Object} options - Call options
   */
  async startCall(assistantId, options = {}) {
    if (!this.webConnected) {
      throw new Error('Web SDK not connected');
    }
    
    console.log('[VapiIntegrationService] Starting voice call', { assistantId, options });
    
    try {
      // Use Web SDK for voice calls
      await this.webClient.start(assistantId, options.assistantOverrides);
      return { success: true, callId: this.webClient.callId };
    } catch (error) {
      console.error('[VapiIntegrationService] Call start failed:', error);
      throw error;
    }
  }

  /**
   * Stop the current voice call
   */
  async stopCall() {
    if (!this.webConnected) {
      throw new Error('Web SDK not connected');
    }
    
    try {
      await this.webClient.stop();
      return { success: true };
    } catch (error) {
      console.error('[VapiIntegrationService] Call stop failed:', error);
      throw error;
    }
  }

  /**
   * Set up event listeners for voice calls
   * @param {Object} callbacks - Event callback functions
   */
  setupCallEventListeners(callbacks) {
    if (!this.webConnected) {
      console.warn('[VapiIntegrationService] Cannot set up event listeners - Web SDK not connected');
      return;
    }

    // Set up all the standard Vapi Web SDK event listeners
    if (callbacks.onCallStart) {
      this.webClient.on('call-start', callbacks.onCallStart);
    }

    if (callbacks.onCallEnd) {
      this.webClient.on('call-end', callbacks.onCallEnd);
    }

    if (callbacks.onMessage) {
      this.webClient.on('message', callbacks.onMessage);
    }

    if (callbacks.onError) {
      this.webClient.on('error', callbacks.onError);
    }

    if (callbacks.onSpeechStart) {
      this.webClient.on('speech-start', callbacks.onSpeechStart);
    }

    if (callbacks.onSpeechEnd) {
      this.webClient.on('speech-end', callbacks.onSpeechEnd);
    }

    if (callbacks.onVolumeLevel) {
      this.webClient.on('volume-level', callbacks.onVolumeLevel);
    }

    console.log('[VapiIntegrationService] Event listeners configured');
  }

  // === ADMINISTRATIVE OPERATIONS (Streamable HTTP) ===

  /**
   * Parse MCP tool response
   * @param {Object} response - MCP response
   * @returns {any} - Parsed response data
   */
  parseMCPResponse(response) {
    if (!response?.content) return response;

    const textItem = response.content.find(item => item.type === 'text');
    if (textItem?.text) {
      try {
        return JSON.parse(textItem.text);
      } catch {
        return textItem.text;
      }
    }
    return response;
  }

  /**
   * List all assistants using HTTP client
   * @returns {Promise<Array>} - List of assistants
   */
  async listAssistants() {
    if (!this.httpConnected) {
      throw new Error('HTTP client not connected');
    }

    try {
      console.log('[VapiIntegrationService] Listing assistants via HTTP');

      const response = await this.httpClient.callTool({
        name: 'list_assistants',
        arguments: {}
      });

      const assistants = this.parseMCPResponse(response);
      console.log('[VapiIntegrationService] Retrieved assistants:', assistants?.length || 0);

      return assistants;
    } catch (error) {
      console.error('[VapiIntegrationService] List assistants failed:', error);
      throw error;
    }
  }

  /**
   * Get assistant by ID using HTTP client
   * @param {string} assistantId - Assistant ID
   * @returns {Promise<Object>} - Assistant data
   */
  async getAssistant(assistantId) {
    if (!this.httpConnected) {
      throw new Error('HTTP client not connected');
    }

    try {
      console.log('[VapiIntegrationService] Getting assistant via HTTP:', assistantId);

      const response = await this.httpClient.callTool({
        name: 'get_assistant',
        arguments: { assistantId }
      });

      const assistant = this.parseMCPResponse(response);
      console.log('[VapiIntegrationService] Retrieved assistant:', assistant?.name || 'Unknown');

      return assistant;
    } catch (error) {
      console.error('[VapiIntegrationService] Get assistant failed:', error);
      throw error;
    }
  }

  /**
   * Create new assistant using HTTP client
   * @param {Object} assistantConfig - Assistant configuration
   * @returns {Promise<Object>} - Created assistant
   */
  async createAssistant(assistantConfig) {
    if (!this.httpConnected) {
      throw new Error('HTTP client not connected');
    }

    try {
      console.log('[VapiIntegrationService] Creating assistant via HTTP:', assistantConfig.name);

      const response = await this.httpClient.callTool({
        name: 'create_assistant',
        arguments: assistantConfig
      });

      const assistant = this.parseMCPResponse(response);
      console.log('[VapiIntegrationService] Created assistant:', assistant?.id);

      return assistant;
    } catch (error) {
      console.error('[VapiIntegrationService] Create assistant failed:', error);
      throw error;
    }
  }

  /**
   * Update existing assistant using HTTP client
   * @param {string} assistantId - Assistant ID
   * @param {Object} updates - Assistant updates
   * @returns {Promise<Object>} - Updated assistant
   */
  async updateAssistant(assistantId, updates) {
    if (!this.httpConnected) {
      throw new Error('HTTP client not connected');
    }

    try {
      console.log('[VapiIntegrationService] Updating assistant via HTTP:', assistantId);

      const response = await this.httpClient.callTool({
        name: 'update_assistant',
        arguments: { assistantId, ...updates }
      });

      const assistant = this.parseMCPResponse(response);
      console.log('[VapiIntegrationService] Updated assistant:', assistant?.id);

      return assistant;
    } catch (error) {
      console.error('[VapiIntegrationService] Update assistant failed:', error);
      throw error;
    }
  }

  // === CALL MANAGEMENT OPERATIONS (Streamable HTTP) ===

  /**
   * Create outbound call using HTTP client
   * @param {Object} callConfig - Call configuration
   * @returns {Promise<Object>} - Created call
   */
  async createCall(callConfig) {
    if (!this.httpConnected) {
      throw new Error('HTTP client not connected');
    }

    try {
      console.log('[VapiIntegrationService] Creating call via HTTP');

      const response = await this.httpClient.callTool({
        name: 'create_call',
        arguments: callConfig
      });

      const call = this.parseMCPResponse(response);
      console.log('[VapiIntegrationService] Created call:', call?.id);

      return call;
    } catch (error) {
      console.error('[VapiIntegrationService] Create call failed:', error);
      throw error;
    }
  }

  /**
   * List calls using HTTP client
   * @returns {Promise<Array>} - List of calls
   */
  async listCalls() {
    if (!this.httpConnected) {
      throw new Error('HTTP client not connected');
    }

    try {
      console.log('[VapiIntegrationService] Listing calls via HTTP');

      const response = await this.httpClient.callTool({
        name: 'list_calls',
        arguments: {}
      });

      const calls = this.parseMCPResponse(response);
      console.log('[VapiIntegrationService] Retrieved calls:', calls?.length || 0);

      return calls;
    } catch (error) {
      console.error('[VapiIntegrationService] List calls failed:', error);
      throw error;
    }
  }

  /**
   * Get call by ID using HTTP client
   * @param {string} callId - Call ID
   * @returns {Promise<Object>} - Call data
   */
  async getCall(callId) {
    if (!this.httpConnected) {
      throw new Error('HTTP client not connected');
    }

    try {
      console.log('[VapiIntegrationService] Getting call via HTTP:', callId);

      const response = await this.httpClient.callTool({
        name: 'get_call',
        arguments: { callId }
      });

      const call = this.parseMCPResponse(response);
      console.log('[VapiIntegrationService] Retrieved call:', call?.status || 'Unknown');

      return call;
    } catch (error) {
      console.error('[VapiIntegrationService] Get call failed:', error);
      throw error;
    }
  }

  // === REAL-TIME OPERATIONS (SSE) ===

  /**
   * Monitor call in real-time using SSE client
   * @param {string} callId - Call ID to monitor
   * @param {Function} onUpdate - Callback for call updates
   */
  async monitorCall(callId, onUpdate) {
    if (!this.sseConnected) {
      console.warn('[VapiIntegrationService] SSE not connected, cannot monitor call');
      return;
    }

    try {
      console.log('[VapiIntegrationService] Starting call monitoring via SSE:', callId);

      // Set up real-time monitoring using SSE
      const monitoringInterval = setInterval(async () => {
        try {
          const callData = await this.getCall(callId);
          if (callData && onUpdate) {
            onUpdate({
              type: 'call-status-update',
              callId,
              status: callData.status,
              data: callData
            });
          }
        } catch (error) {
          console.warn('[VapiIntegrationService] Call monitoring update failed:', error);
        }
      }, 2000); // Poll every 2 seconds

      // Return cleanup function
      return () => {
        clearInterval(monitoringInterval);
        console.log('[VapiIntegrationService] Call monitoring stopped for:', callId);
      };

    } catch (error) {
      console.error('[VapiIntegrationService] Call monitoring failed:', error);
      throw error;
    }
  }

  // === SMS NOTIFICATION SYSTEM ===

  /**
   * Send SMS notification to attorney about ongoing session
   * @param {Object} notificationData - Notification data
   * @returns {Promise<Object>} - SMS send result
   */
  async sendAttorneyNotification(notificationData) {
    const {
      attorneyPhone,
      clientInfo,
      callId,
      sessionType = 'consultation',
      urgency = 'normal'
    } = notificationData;

    try {
      console.log('[VapiIntegrationService] Sending attorney notification via SMS');

      // Generate secure call control link
      const controlLink = await this.generateCallControlLink(callId, attorneyPhone);

      // Create SMS message
      const message = this.createNotificationMessage({
        clientInfo,
        sessionType,
        urgency,
        controlLink
      });

      // Send SMS using Vapi's SMS capabilities or external service
      const smsResult = await this.sendSMS(attorneyPhone, message);

      console.log('[VapiIntegrationService] Attorney notification sent:', smsResult);
      return smsResult;

    } catch (error) {
      console.error('[VapiIntegrationService] Attorney notification failed:', error);
      throw error;
    }
  }

  /**
   * Generate secure call control link for attorney
   * @param {string} callId - Call ID
   * @param {string} attorneyPhone - Attorney phone number for verification
   * @returns {Promise<string>} - Secure control link
   */
  async generateCallControlLink(callId, attorneyPhone) {
    try {
      // Generate secure token with expiration
      const token = await this.generateSecureToken({
        callId,
        attorneyPhone,
        permissions: ['monitor', 'control', 'takeover'],
        expiresIn: '2h' // 2 hours expiration
      });

      // Create control link
      const baseUrl = window.location.origin;
      const controlLink = `${baseUrl}/call-control?token=${token}&callId=${callId}`;

      console.log('[VapiIntegrationService] Generated call control link');
      return controlLink;

    } catch (error) {
      console.error('[VapiIntegrationService] Call control link generation failed:', error);
      throw error;
    }
  }

  /**
   * Generate secure token for call control access
   * @param {Object} tokenData - Token data
   * @returns {Promise<string>} - Secure token
   */
  async generateSecureToken(tokenData) {
    try {
      // In a real implementation, this would use JWT or similar
      // For now, create a simple secure token
      const payload = {
        ...tokenData,
        timestamp: Date.now(),
        nonce: Math.random().toString(36).substring(2, 15)
      };

      // Base64 encode the payload (in production, use proper JWT signing)
      const token = btoa(JSON.stringify(payload));

      return token;

    } catch (error) {
      console.error('[VapiIntegrationService] Token generation failed:', error);
      throw error;
    }
  }

  /**
   * Create notification message for attorney
   * @param {Object} messageData - Message data
   * @returns {string} - Formatted message
   */
  createNotificationMessage({ clientInfo, sessionType, urgency, controlLink }) {
    const urgencyPrefix = urgency === 'high' ? '🚨 URGENT: ' : '';
    const clientName = clientInfo?.name || 'A client';

    return `${urgencyPrefix}${clientName} has started a ${sessionType} session. ` +
           `Monitor and control the call here: ${controlLink}`;
  }

  /**
   * Send SMS message
   * @param {string} phoneNumber - Phone number to send to
   * @param {string} message - Message content
   * @returns {Promise<Object>} - SMS result
   */
  async sendSMS(phoneNumber, message) {
    try {
      // This would integrate with your SMS provider (Twilio, etc.)
      // For now, return a mock response
      console.log('[VapiIntegrationService] SMS would be sent to:', phoneNumber);
      console.log('[VapiIntegrationService] Message:', message);

      return {
        success: true,
        messageId: `sms_${Date.now()}`,
        phoneNumber,
        message,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      console.error('[VapiIntegrationService] SMS sending failed:', error);
      throw error;
    }
  }

  // === CALL CONTROL AND INTERVENTION ===

  /**
   * Verify call control token and permissions
   * @param {string} token - Control token
   * @param {string} callId - Call ID
   * @returns {Promise<Object>} - Verification result
   */
  async verifyCallControlAccess(token, callId) {
    try {
      // Decode and verify token (in production, use proper JWT verification)
      const payload = JSON.parse(atob(token));

      // Check expiration
      const expirationTime = payload.timestamp + (2 * 60 * 60 * 1000); // 2 hours
      if (Date.now() > expirationTime) {
        throw new Error('Token expired');
      }

      // Verify call ID matches
      if (payload.callId !== callId) {
        throw new Error('Invalid call ID');
      }

      console.log('[VapiIntegrationService] Call control access verified');
      return {
        valid: true,
        permissions: payload.permissions,
        attorneyPhone: payload.attorneyPhone
      };

    } catch (error) {
      console.error('[VapiIntegrationService] Call control access verification failed:', error);
      return {
        valid: false,
        error: error.message
      };
    }
  }

  /**
   * Take over an ongoing call (attorney intervention)
   * @param {string} callId - Call ID
   * @param {Object} takeoverOptions - Takeover options
   * @returns {Promise<Object>} - Takeover result
   */
  async takeoverCall(callId, takeoverOptions = {}) {
    try {
      console.log('[VapiIntegrationService] Initiating call takeover:', callId);

      // Get current call data
      const callData = await this.getCall(callId);
      if (!callData) {
        throw new Error('Call not found');
      }

      // Send intervention message to the call
      const interventionMessage = takeoverOptions.message ||
        "An attorney is now joining the conversation to assist you directly.";

      // This would typically involve:
      // 1. Pausing the AI assistant
      // 2. Notifying the client about attorney intervention
      // 3. Setting up attorney connection to the call

      const result = {
        success: true,
        callId,
        interventionTime: new Date().toISOString(),
        message: interventionMessage
      };

      console.log('[VapiIntegrationService] Call takeover initiated:', result);
      return result;

    } catch (error) {
      console.error('[VapiIntegrationService] Call takeover failed:', error);
      throw error;
    }
  }

  /**
   * Send real-time guidance to AI assistant during call
   * @param {string} callId - Call ID
   * @param {string} guidance - Guidance message for AI
   * @returns {Promise<Object>} - Guidance result
   */
  async sendRealTimeGuidance(callId, guidance) {
    try {
      console.log('[VapiIntegrationService] Sending real-time guidance:', callId);

      // This would send guidance to the AI assistant during the call
      // The AI would incorporate this guidance into its responses

      const guidanceMessage = {
        type: 'system-guidance',
        content: guidance,
        timestamp: new Date().toISOString(),
        callId
      };

      // In a real implementation, this would use Vapi's real-time messaging
      console.log('[VapiIntegrationService] Guidance sent:', guidanceMessage);

      return {
        success: true,
        guidanceId: `guidance_${Date.now()}`,
        message: guidanceMessage
      };

    } catch (error) {
      console.error('[VapiIntegrationService] Real-time guidance failed:', error);
      throw error;
    }
  }

  /**
   * Get real-time call transcript and status
   * @param {string} callId - Call ID
   * @returns {Promise<Object>} - Call transcript and status
   */
  async getCallTranscript(callId) {
    try {
      console.log('[VapiIntegrationService] Getting call transcript:', callId);

      const callData = await this.getCall(callId);
      if (!callData) {
        throw new Error('Call not found');
      }

      // Extract transcript from call data
      const transcript = callData.transcript || [];
      const status = callData.status;
      const duration = callData.duration || 0;

      return {
        callId,
        status,
        duration,
        transcript,
        lastUpdate: new Date().toISOString()
      };

    } catch (error) {
      console.error('[VapiIntegrationService] Get call transcript failed:', error);
      throw error;
    }
  }

  // === UTILITY METHODS ===

  /**
   * Check connection status
   * @returns {Object} - Connection status for all transports
   */
  getConnectionStatus() {
    return {
      web: this.webConnected,
      http: this.httpConnected,
      sse: this.sseConnected,
      overall: this.webConnected || this.httpConnected
    };
  }

  /**
   * Retry operation with exponential backoff
   * @param {Function} operation - Operation to retry
   * @param {number} maxRetries - Maximum number of retries
   * @returns {Promise<any>} - Operation result
   */
  async retryWithBackoff(operation, maxRetries = this.maxRetries) {
    let lastError;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;
        console.warn(`[VapiIntegrationService] Operation failed (attempt ${attempt}/${maxRetries}):`, error.message);

        if (attempt < maxRetries) {
          const delay = this.retryDelay * Math.pow(2, attempt - 1);
          console.log(`[VapiIntegrationService] Retrying in ${delay}ms...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    throw lastError;
  }

  /**
   * Health check for all transport connections
   * @returns {Promise<Object>} - Health status
   */
  async healthCheck() {
    const health = {
      timestamp: new Date().toISOString(),
      overall: 'healthy',
      transports: {}
    };

    // Check Web SDK
    try {
      health.transports.web = {
        connected: this.webConnected,
        status: this.webConnected ? 'healthy' : 'disconnected'
      };
    } catch (error) {
      health.transports.web = {
        connected: false,
        status: 'error',
        error: error.message
      };
    }

    // Check HTTP client
    try {
      if (this.httpConnected) {
        // Try a simple operation to verify connection
        await this.retryWithBackoff(async () => {
          const tools = await this.httpClient.listTools();
          return tools;
        }, 1);

        health.transports.http = {
          connected: true,
          status: 'healthy'
        };
      } else {
        health.transports.http = {
          connected: false,
          status: 'disconnected'
        };
      }
    } catch (error) {
      health.transports.http = {
        connected: false,
        status: 'error',
        error: error.message
      };
    }

    // Check SSE client
    health.transports.sse = {
      connected: this.sseConnected,
      status: this.sseConnected ? 'healthy' : 'disconnected'
    };

    // Determine overall health
    const hasWorkingTransport = health.transports.web.connected ||
                               health.transports.http.connected;

    if (!hasWorkingTransport) {
      health.overall = 'critical';
    } else if (!health.transports.web.connected || !health.transports.http.connected) {
      health.overall = 'degraded';
    }

    console.log('[VapiIntegrationService] Health check completed:', health.overall);
    return health;
  }

  /**
   * Reconnect failed transports
   * @returns {Promise<Object>} - Reconnection results
   */
  async reconnect() {
    console.log('[VapiIntegrationService] Attempting to reconnect failed transports');

    const results = {
      web: false,
      http: false,
      sse: false
    };

    // Reconnect Web SDK if needed
    if (!this.webConnected && this.publicKey) {
      try {
        results.web = await this.initializeWebClient();
      } catch (error) {
        console.error('[VapiIntegrationService] Web SDK reconnection failed:', error);
      }
    } else {
      results.web = this.webConnected;
    }

    // Reconnect HTTP client if needed
    if (!this.httpConnected && this.apiKey) {
      try {
        results.http = await this.initializeHTTPClient();
      } catch (error) {
        console.error('[VapiIntegrationService] HTTP client reconnection failed:', error);
      }
    } else {
      results.http = this.httpConnected;
    }

    // Reconnect SSE client if needed
    if (!this.sseConnected && this.apiKey) {
      try {
        results.sse = await this.initializeSSEClient();
      } catch (error) {
        console.error('[VapiIntegrationService] SSE client reconnection failed:', error);
      }
    } else {
      results.sse = this.sseConnected;
    }

    console.log('[VapiIntegrationService] Reconnection results:', results);
    return results;
  }

  /**
   * Disconnect all clients
   */
  async disconnect() {
    console.log('[VapiIntegrationService] Disconnecting all clients');

    if (this.httpClient && this.httpConnected) {
      try {
        await this.httpClient.close();
        this.httpConnected = false;
      } catch (error) {
        console.error('[VapiIntegrationService] HTTP client disconnect failed:', error);
      }
    }

    if (this.sseClient && this.sseConnected) {
      try {
        await this.sseClient.close();
        this.sseConnected = false;
      } catch (error) {
        console.error('[VapiIntegrationService] SSE client disconnect failed:', error);
      }
    }

    // Web client doesn't need explicit disconnection
    this.webConnected = false;

    console.log('[VapiIntegrationService] All clients disconnected');
  }
}

export default VapiIntegrationService;
