/**
 * Unified Vapi Integration Service
 * 
 * Implements the dual transport pattern recommended in the new guidelines:
 * - SSE (Server-Sent Events) for real-time operations
 * - Streamable HTTP for administrative operations
 * 
 * Based on latest Vapi documentation:
 * - https://docs.vapi.ai/sdk/web
 * - https://docs.vapi.ai/sdk/mcp-server
 */

import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { SSEClientTransport } from '@modelcontextprotocol/sdk/client/sse.js';
import { StreamableHTTPClientTransport } from '@modelcontextprotocol/sdk/client/streamableHttp.js';
import Vapi from '@vapi-ai/web';

class VapiIntegrationService {
  constructor() {
    // Transport clients
    this.sseClient = null;
    this.httpClient = null;
    this.webClient = null;
    
    // Connection states
    this.sseConnected = false;
    this.httpConnected = false;
    this.webConnected = false;
    
    // Configuration
    this.apiKey = null;
    this.publicKey = null;
    
    // URLs
    this.mcpSSEUrl = 'https://mcp.vapi.ai/sse';
    this.mcpHTTPUrl = 'https://mcp.vapi.ai/mcp';
    
    // Error handling
    this.maxRetries = 3;
    this.retryDelay = 1000;
  }

  /**
   * Initialize the service with API keys
   * @param {string} publicKey - Vapi public API key for client-side operations
   * @param {string} privateKey - Vapi private API key for server-side operations
   */
  async initialize(publicKey, privateKey) {
    this.publicKey = publicKey;
    this.apiKey = privateKey;
    
    console.log('[VapiIntegrationService] Initializing with dual transport pattern');
    
    // Initialize Web SDK for client-side voice calls
    await this.initializeWebClient();
    
    // Initialize MCP clients for administrative operations
    await this.initializeHTTPClient();
    await this.initializeSSEClient();
    
    return {
      web: this.webConnected,
      http: this.httpConnected,
      sse: this.sseConnected
    };
  }

  /**
   * Initialize Vapi Web SDK for voice calls
   */
  async initializeWebClient() {
    try {
      if (!this.publicKey) {
        throw new Error('Public API key required for Web SDK');
      }
      
      this.webClient = new Vapi(this.publicKey);
      this.webConnected = true;
      
      console.log('[VapiIntegrationService] ✅ Web SDK initialized');
      return true;
    } catch (error) {
      console.error('[VapiIntegrationService] ❌ Web SDK initialization failed:', error);
      this.webConnected = false;
      return false;
    }
  }

  /**
   * Initialize Streamable HTTP client for administrative operations
   */
  async initializeHTTPClient() {
    try {
      if (!this.apiKey) {
        throw new Error('Private API key required for HTTP client');
      }
      
      this.httpClient = new Client({
        name: 'legalscout-http-client',
        version: '1.0.0'
      });
      
      const transport = new StreamableHTTPClientTransport(
        new URL(this.mcpHTTPUrl),
        {
          requestInit: {
            headers: {
              'Authorization': `Bearer ${this.apiKey}`
            }
          }
        }
      );
      
      await this.httpClient.connect(transport);
      this.httpConnected = true;
      
      console.log('[VapiIntegrationService] ✅ HTTP client connected');
      return true;
    } catch (error) {
      console.error('[VapiIntegrationService] ❌ HTTP client connection failed:', error);
      this.httpConnected = false;
      return false;
    }
  }

  /**
   * Initialize SSE client for real-time operations
   */
  async initializeSSEClient() {
    try {
      if (!this.apiKey) {
        throw new Error('Private API key required for SSE client');
      }
      
      this.sseClient = new Client({
        name: 'legalscout-sse-client',
        version: '1.0.0'
      });
      
      const transport = new SSEClientTransport({
        url: this.mcpSSEUrl,
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
          'Accept': 'text/event-stream',
          'Cache-Control': 'no-cache'
        }
      });
      
      await this.sseClient.connect(transport);
      this.sseConnected = true;
      
      console.log('[VapiIntegrationService] ✅ SSE client connected');
      return true;
    } catch (error) {
      console.error('[VapiIntegrationService] ❌ SSE client connection failed:', error);
      this.sseConnected = false;
      return false;
    }
  }

  // === VOICE CALL OPERATIONS (Web SDK) ===

  /**
   * Start a voice call using the Web SDK
   * @param {string} assistantId - Assistant ID to use for the call
   * @param {Object} options - Call options
   */
  async startCall(assistantId, options = {}) {
    if (!this.webConnected) {
      throw new Error('Web SDK not connected');
    }
    
    console.log('[VapiIntegrationService] Starting voice call', { assistantId, options });
    
    try {
      // Use Web SDK for voice calls
      await this.webClient.start(assistantId, options.assistantOverrides);
      return { success: true, callId: this.webClient.callId };
    } catch (error) {
      console.error('[VapiIntegrationService] Call start failed:', error);
      throw error;
    }
  }

  /**
   * Stop the current voice call
   */
  async stopCall() {
    if (!this.webConnected) {
      throw new Error('Web SDK not connected');
    }
    
    try {
      await this.webClient.stop();
      return { success: true };
    } catch (error) {
      console.error('[VapiIntegrationService] Call stop failed:', error);
      throw error;
    }
  }

  /**
   * Set up event listeners for voice calls
   * @param {Object} callbacks - Event callback functions
   */
  setupCallEventListeners(callbacks) {
    if (!this.webConnected) {
      console.warn('[VapiIntegrationService] Cannot set up event listeners - Web SDK not connected');
      return;
    }

    // Set up all the standard Vapi Web SDK event listeners
    if (callbacks.onCallStart) {
      this.webClient.on('call-start', callbacks.onCallStart);
    }

    if (callbacks.onCallEnd) {
      this.webClient.on('call-end', callbacks.onCallEnd);
    }

    if (callbacks.onMessage) {
      this.webClient.on('message', callbacks.onMessage);
    }

    if (callbacks.onError) {
      this.webClient.on('error', callbacks.onError);
    }

    if (callbacks.onSpeechStart) {
      this.webClient.on('speech-start', callbacks.onSpeechStart);
    }

    if (callbacks.onSpeechEnd) {
      this.webClient.on('speech-end', callbacks.onSpeechEnd);
    }

    if (callbacks.onVolumeLevel) {
      this.webClient.on('volume-level', callbacks.onVolumeLevel);
    }

    console.log('[VapiIntegrationService] Event listeners configured');
  }

  // === ADMINISTRATIVE OPERATIONS (Streamable HTTP) ===

  /**
   * Parse MCP tool response
   * @param {Object} response - MCP response
   * @returns {any} - Parsed response data
   */
  parseMCPResponse(response) {
    if (!response?.content) return response;

    const textItem = response.content.find(item => item.type === 'text');
    if (textItem?.text) {
      try {
        return JSON.parse(textItem.text);
      } catch {
        return textItem.text;
      }
    }
    return response;
  }

  /**
   * List all assistants using HTTP client
   * @returns {Promise<Array>} - List of assistants
   */
  async listAssistants() {
    if (!this.httpConnected) {
      throw new Error('HTTP client not connected');
    }

    try {
      console.log('[VapiIntegrationService] Listing assistants via HTTP');

      const response = await this.httpClient.callTool({
        name: 'list_assistants',
        arguments: {}
      });

      const assistants = this.parseMCPResponse(response);
      console.log('[VapiIntegrationService] Retrieved assistants:', assistants?.length || 0);

      return assistants;
    } catch (error) {
      console.error('[VapiIntegrationService] List assistants failed:', error);
      throw error;
    }
  }

  /**
   * Get assistant by ID using HTTP client
   * @param {string} assistantId - Assistant ID
   * @returns {Promise<Object>} - Assistant data
   */
  async getAssistant(assistantId) {
    if (!this.httpConnected) {
      throw new Error('HTTP client not connected');
    }

    try {
      console.log('[VapiIntegrationService] Getting assistant via HTTP:', assistantId);

      const response = await this.httpClient.callTool({
        name: 'get_assistant',
        arguments: { assistantId }
      });

      const assistant = this.parseMCPResponse(response);
      console.log('[VapiIntegrationService] Retrieved assistant:', assistant?.name || 'Unknown');

      return assistant;
    } catch (error) {
      console.error('[VapiIntegrationService] Get assistant failed:', error);
      throw error;
    }
  }

  /**
   * Create new assistant using HTTP client
   * @param {Object} assistantConfig - Assistant configuration
   * @returns {Promise<Object>} - Created assistant
   */
  async createAssistant(assistantConfig) {
    if (!this.httpConnected) {
      throw new Error('HTTP client not connected');
    }

    try {
      console.log('[VapiIntegrationService] Creating assistant via HTTP:', assistantConfig.name);

      const response = await this.httpClient.callTool({
        name: 'create_assistant',
        arguments: assistantConfig
      });

      const assistant = this.parseMCPResponse(response);
      console.log('[VapiIntegrationService] Created assistant:', assistant?.id);

      return assistant;
    } catch (error) {
      console.error('[VapiIntegrationService] Create assistant failed:', error);
      throw error;
    }
  }

  /**
   * Update existing assistant using HTTP client
   * @param {string} assistantId - Assistant ID
   * @param {Object} updates - Assistant updates
   * @returns {Promise<Object>} - Updated assistant
   */
  async updateAssistant(assistantId, updates) {
    if (!this.httpConnected) {
      throw new Error('HTTP client not connected');
    }

    try {
      console.log('[VapiIntegrationService] Updating assistant via HTTP:', assistantId);

      const response = await this.httpClient.callTool({
        name: 'update_assistant',
        arguments: { assistantId, ...updates }
      });

      const assistant = this.parseMCPResponse(response);
      console.log('[VapiIntegrationService] Updated assistant:', assistant?.id);

      return assistant;
    } catch (error) {
      console.error('[VapiIntegrationService] Update assistant failed:', error);
      throw error;
    }
  }

  // === CALL MANAGEMENT OPERATIONS (Streamable HTTP) ===

  /**
   * Create outbound call using HTTP client
   * @param {Object} callConfig - Call configuration
   * @returns {Promise<Object>} - Created call
   */
  async createCall(callConfig) {
    if (!this.httpConnected) {
      throw new Error('HTTP client not connected');
    }

    try {
      console.log('[VapiIntegrationService] Creating call via HTTP');

      const response = await this.httpClient.callTool({
        name: 'create_call',
        arguments: callConfig
      });

      const call = this.parseMCPResponse(response);
      console.log('[VapiIntegrationService] Created call:', call?.id);

      return call;
    } catch (error) {
      console.error('[VapiIntegrationService] Create call failed:', error);
      throw error;
    }
  }

  /**
   * List calls using HTTP client
   * @returns {Promise<Array>} - List of calls
   */
  async listCalls() {
    if (!this.httpConnected) {
      throw new Error('HTTP client not connected');
    }

    try {
      console.log('[VapiIntegrationService] Listing calls via HTTP');

      const response = await this.httpClient.callTool({
        name: 'list_calls',
        arguments: {}
      });

      const calls = this.parseMCPResponse(response);
      console.log('[VapiIntegrationService] Retrieved calls:', calls?.length || 0);

      return calls;
    } catch (error) {
      console.error('[VapiIntegrationService] List calls failed:', error);
      throw error;
    }
  }

  /**
   * Get call by ID using HTTP client
   * @param {string} callId - Call ID
   * @returns {Promise<Object>} - Call data
   */
  async getCall(callId) {
    if (!this.httpConnected) {
      throw new Error('HTTP client not connected');
    }

    try {
      console.log('[VapiIntegrationService] Getting call via HTTP:', callId);

      const response = await this.httpClient.callTool({
        name: 'get_call',
        arguments: { callId }
      });

      const call = this.parseMCPResponse(response);
      console.log('[VapiIntegrationService] Retrieved call:', call?.status || 'Unknown');

      return call;
    } catch (error) {
      console.error('[VapiIntegrationService] Get call failed:', error);
      throw error;
    }
  }

  // === REAL-TIME OPERATIONS (SSE) ===

  /**
   * Monitor call in real-time using SSE client
   * @param {string} callId - Call ID to monitor
   * @param {Function} onUpdate - Callback for call updates
   */
  async monitorCall(callId, onUpdate) {
    if (!this.sseConnected) {
      console.warn('[VapiIntegrationService] SSE not connected, cannot monitor call');
      return;
    }

    try {
      console.log('[VapiIntegrationService] Starting call monitoring via SSE:', callId);

      // Set up SSE monitoring for real-time call updates
      // This would typically involve setting up event listeners
      // for call status changes, transcript updates, etc.

      // Implementation would depend on specific SSE events available
      // from Vapi's MCP server

    } catch (error) {
      console.error('[VapiIntegrationService] Call monitoring failed:', error);
      throw error;
    }
  }

  // === UTILITY METHODS ===

  /**
   * Check connection status
   * @returns {Object} - Connection status for all transports
   */
  getConnectionStatus() {
    return {
      web: this.webConnected,
      http: this.httpConnected,
      sse: this.sseConnected,
      overall: this.webConnected || this.httpConnected
    };
  }

  /**
   * Disconnect all clients
   */
  async disconnect() {
    console.log('[VapiIntegrationService] Disconnecting all clients');

    if (this.httpClient && this.httpConnected) {
      try {
        await this.httpClient.close();
        this.httpConnected = false;
      } catch (error) {
        console.error('[VapiIntegrationService] HTTP client disconnect failed:', error);
      }
    }

    if (this.sseClient && this.sseConnected) {
      try {
        await this.sseClient.close();
        this.sseConnected = false;
      } catch (error) {
        console.error('[VapiIntegrationService] SSE client disconnect failed:', error);
      }
    }

    // Web client doesn't need explicit disconnection
    this.webConnected = false;

    console.log('[VapiIntegrationService] All clients disconnected');
  }
}

export default VapiIntegrationService;
