<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Meta MCP Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #333;
        }
        .section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .log {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            white-space: pre-wrap;
        }
        button {
            padding: 8px 16px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #45a049;
        }
        input, textarea {
            width: 100%;
            padding: 8px;
            margin-bottom: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        textarea {
            height: 100px;
        }
    </style>
</head>
<body>
    <h1>AI Meta MCP Test</h1>
    
    <div class="section">
        <h2>Create Function</h2>
        <div>
            <label for="functionName">Function Name:</label>
            <input type="text" id="functionName" value="calculate_compound_interest">
        </div>
        <div>
            <label for="functionDescription">Description:</label>
            <input type="text" id="functionDescription" value="Calculate compound interest given principal, rate, time, and compounding frequency">
        </div>
        <div>
            <label for="functionCode">Implementation Code:</label>
            <textarea id="functionCode">
const principal = params.principal;
const rate = params.rate;
const time = params.time;
const frequency = params.frequency;

const amount = principal * Math.pow(1 + rate/frequency, frequency * time);
const interest = amount - principal;

return {
  principal: principal,
  amount: amount,
  interest: interest
};
            </textarea>
        </div>
        <div>
            <label for="parametersSchema">Parameters Schema:</label>
            <textarea id="parametersSchema">
{
  "principal": {
    "type": "number",
    "description": "The initial principal amount"
  },
  "rate": {
    "type": "number",
    "description": "The annual interest rate (as a decimal)"
  },
  "time": {
    "type": "number",
    "description": "The time in years"
  },
  "frequency": {
    "type": "number",
    "description": "The number of times interest is compounded per year"
  }
}
            </textarea>
        </div>
        <button id="createFunction">Create Function</button>
    </div>
    
    <div class="section">
        <h2>Test Function</h2>
        <div>
            <label for="testFunctionName">Function Name:</label>
            <input type="text" id="testFunctionName" value="calculate_compound_interest">
        </div>
        <div>
            <label for="testParameters">Parameters:</label>
            <textarea id="testParameters">
{
  "principal": 1000,
  "rate": 0.05,
  "time": 5,
  "frequency": 12
}
            </textarea>
        </div>
        <button id="testFunction">Test Function</button>
    </div>
    
    <div class="section">
        <h2>List Functions</h2>
        <button id="listFunctions">List Functions</button>
    </div>
    
    <div class="section">
        <h2>Log</h2>
        <div id="log" class="log"></div>
    </div>
    
    <script>
        // Log function
        function log(message) {
            const logElement = document.getElementById('log');
            logElement.innerHTML += message + '\n';
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        // Create function
        document.getElementById('createFunction').addEventListener('click', async () => {
            try {
                const functionName = document.getElementById('functionName').value.trim();
                const functionDescription = document.getElementById('functionDescription').value.trim();
                const functionCode = document.getElementById('functionCode').value.trim();
                const parametersSchema = JSON.parse(document.getElementById('parametersSchema').value.trim());
                
                log(`Creating function: ${functionName}...`);
                
                // Call the AI Meta MCP Server to create the function
                const response = await fetch('/api/ai-meta-mcp/create-function', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        name: functionName,
                        description: functionDescription,
                        parameters_schema: parametersSchema,
                        implementation_code: functionCode,
                        execution_environment: 'javascript'
                    })
                });
                
                const result = await response.json();
                log(`Function created: ${JSON.stringify(result, null, 2)}`);
            } catch (error) {
                log(`Error creating function: ${error.message}`);
            }
        });
        
        // Test function
        document.getElementById('testFunction').addEventListener('click', async () => {
            try {
                const functionName = document.getElementById('testFunctionName').value.trim();
                const parameters = JSON.parse(document.getElementById('testParameters').value.trim());
                
                log(`Testing function: ${functionName}...`);
                
                // Call the AI Meta MCP Server to test the function
                const response = await fetch('/api/ai-meta-mcp/call-function', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        name: functionName,
                        arguments: parameters
                    })
                });
                
                const result = await response.json();
                log(`Function result: ${JSON.stringify(result, null, 2)}`);
            } catch (error) {
                log(`Error testing function: ${error.message}`);
            }
        });
        
        // List functions
        document.getElementById('listFunctions').addEventListener('click', async () => {
            try {
                log('Listing functions...');
                
                // Call the AI Meta MCP Server to list functions
                const response = await fetch('/api/ai-meta-mcp/list-functions');
                const result = await response.json();
                
                log(`Functions: ${JSON.stringify(result, null, 2)}`);
            } catch (error) {
                log(`Error listing functions: ${error.message}`);
            }
        });
        
        // Initial log
        log('AI Meta MCP Test Page Loaded');
    </script>
</body>
</html>
