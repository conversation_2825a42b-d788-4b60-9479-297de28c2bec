# 🏗️ LegalScout Voice - Application Architecture

## 📊 System Overview Diagram

```mermaid
graph TB
    %% Client Layer
    subgraph "🌐 Client Layer"
        WEB[🖥️ Web Browser<br/>Desktop Users]
        MOBILE[📱 Mobile Browser<br/>Mobile Users]
        SUBDOMAIN[🏢 Attorney Subdomains<br/>attorney.legalscout.net]
    end

    %% Frontend Layer
    subgraph "⚛️ Frontend Application (React + Vite)"
        APP[🎯 App.jsx<br/>Main Router & State]
        HOME[🏠 Home Page<br/>Landing + Voice Interface]
        DASH[📊 Dashboard<br/>Attorney Configuration]
        PREVIEW[👁️ Preview System<br/>Real-time Updates]
        AUTH[🔐 Auth Components<br/>OAuth Flow Management]

        APP --> HOME
        APP --> DASH
        APP --> PREVIEW
        APP --> AUTH
    end

    %% API Layer
    subgraph "🔌 API Layer (Vercel Serverless)"
        ROUTER[🎛️ api/index.js<br/>Consolidated Router]
        VAPI_PROXY[🎤 Vapi Proxy<br/>MCP Integration]
        AUTH_CB[🔑 Auth Callback<br/>OAuth Handler]
        WEBHOOK[📡 Webhooks<br/>Call Events]
        BUG[🐛 Bug Reporter<br/>Slack Integration]

        ROUTER --> VAPI_PROXY
        ROUTER --> AUTH_CB
        ROUTER --> WEBHOOK
        ROUTER --> BUG
    end

    %% Services Layer
    subgraph "🛠️ External Services"
        SUPABASE[(🗄️ Supabase<br/>Database + Auth + Storage)]
        VAPI[🎙️ Vapi.ai<br/>Voice AI Platform]
        MCP[🔗 MCP Server<br/>Service Orchestration]
        GMAIL[📧 Gmail OAuth<br/>Authentication Provider]
        VERCEL[☁️ Vercel<br/>Hosting & Deployment]
    end

    %% Data Flow Connections
    WEB --> APP
    MOBILE --> APP
    SUBDOMAIN --> APP

    APP --> ROUTER
    ROUTER --> SUPABASE
    ROUTER --> VAPI
    ROUTER --> MCP

    AUTH --> GMAIL
    VAPI_PROXY --> VAPI
    WEBHOOK --> VAPI

    ROUTER -.-> VERCEL

    %% Styling
    classDef frontend fill:#61dafb,stroke:#21759b,stroke-width:3px,color:#000
    classDef api fill:#ff6b6b,stroke:#c92a2a,stroke-width:3px,color:#fff
    classDef service fill:#51cf66,stroke:#37b24d,stroke-width:3px,color:#000
    classDef client fill:#ffd43b,stroke:#fab005,stroke-width:3px,color:#000

    class APP,HOME,DASH,PREVIEW,AUTH frontend
    class ROUTER,VAPI_PROXY,AUTH_CB,WEBHOOK,BUG api
    class SUPABASE,VAPI,MCP,GMAIL,VERCEL service
    class WEB,MOBILE,SUBDOMAIN client
```

## 🎯 Core Components

### 🌐 Client Layer
- **Web Browser**: Desktop users accessing the main application
- **Mobile Browser**: Mobile users with responsive interface
- **Attorney Subdomains**: Branded attorney pages (e.g., `damon.legalscout.net`)

### ⚛️ Frontend Application
- **App.jsx**: Main router, state management, subdomain detection
- **Home Page**: Landing page with voice interface and attorney discovery
- **Dashboard**: Attorney configuration panel with real-time preview
- **Preview System**: Live preview of attorney configurations
- **Auth Components**: OAuth flow management and user authentication

### 🔌 API Layer
- **Consolidated Router**: Single entry point for all API requests (Vercel limit optimization)
- **Vapi Proxy**: MCP integration for voice assistant management
- **Auth Callback**: OAuth callback handling for Gmail authentication
- **Webhooks**: Real-time call event processing
- **Bug Reporter**: Automated bug reporting to Slack

### 🛠️ External Services
- **Supabase**: PostgreSQL database, authentication, file storage
- **Vapi.ai**: Voice AI platform for real-time conversations
- **MCP Server**: Service orchestration and integration management
- **Gmail OAuth**: Authentication provider
- **Vercel**: Serverless hosting and deployment platform

## 📋 Key Features

### 🎤 Voice Interface
- Real-time voice conversations with AI assistants
- Custom voice configurations per attorney
- Automatic transcription and response generation
- Call recording and analysis

### 🏢 Attorney Dashboard
- Profile and branding customization
- AI assistant configuration
- Practice area management
- Real-time preview system
- Banner and image upload/removal (with persistence fix)

### 🌍 Subdomain System
- Custom attorney subdomains
- Dynamic configuration loading
- Branded client experiences
- SEO-optimized attorney pages

### 🔐 Authentication & Security
- Gmail OAuth integration
- Supabase Row-Level Security
- Secure API endpoints
- Session management

## 🔄 Data Flow

### 📞 Voice Consultation Flow
1. Client visits attorney subdomain
2. Initiates voice conversation
3. Vapi.ai processes voice input
4. AI generates contextual responses
5. Conversation data stored in Supabase
6. Attorney receives notification

### ⚙️ Configuration Flow
1. Attorney logs in via Gmail OAuth
2. Dashboard loads attorney profile from Supabase
3. Real-time preview updates as attorney makes changes
4. Changes saved to Supabase with immediate persistence
5. Vapi assistant updated via MCP integration

## 🚀 Deployment Architecture

- **Frontend**: Static React build deployed to Vercel
- **API**: Serverless functions on Vercel (12 function limit optimization)
- **Database**: Supabase PostgreSQL with real-time subscriptions
- **Storage**: Supabase Storage for files and media
- **CDN**: Vercel Edge Network for global distribution

## 🧩 Component Hierarchy

```mermaid
graph TD
    %% Main App Structure
    APP[🎯 App.jsx]

    %% Core Components
    APP --> HOME[🏠 Home Component]
    APP --> DASH[📊 Dashboard Component]
    APP --> AUTH[🔐 AuthOverlay]
    APP --> PREVIEW[👁️ SimplePreviewPage]

    %% Home Page Components
    HOME --> VOICE[🎤 VapiCall]
    HOME --> MAP[🗺️ MapView]
    HOME --> GLOBE[🌍 GlobeDossierView]
    HOME --> NAVBAR[🧭 Navbar]
    HOME --> BG[🌌 AnimatedBackground]

    %% Dashboard Components
    DASH --> PROFILE[👤 ProfileTab]
    DASH --> AGENT[🤖 AgentTab]
    DASH --> CONSULT[📋 ConsultationsTab]
    DASH --> SHARE[📤 ShareTab]
    DASH --> SETTINGS[⚙️ SettingsTab]

    %% Agent Tab Sub-components
    AGENT --> BANNER[🖼️ Banner Upload/Remove]
    AGENT --> COLORS[🎨 Color Pickers]
    AGENT --> VOICE_CONFIG[🎙️ Voice Configuration]
    AGENT --> AI_CONFIG[🧠 AI Model Settings]

    %% Preview System
    PREVIEW --> IFRAME[🖼️ Preview Iframe]
    PREVIEW --> CONFIG[⚙️ Config Handler]
    PREVIEW --> POSTMSG[📨 PostMessage API]

    %% Authentication Flow
    AUTH --> OAUTH[🔑 OAuth Handler]
    AUTH --> CALLBACK[↩️ Auth Callback]
    AUTH --> SESSION[🎫 Session Manager]

    %% Voice Interface
    VOICE --> TRANSCRIPT[📝 Transcription]
    VOICE --> AUDIO[🔊 Audio Controls]
    VOICE --> STATUS[📊 Call Status]

    %% Styling
    classDef main fill:#4a90e2,stroke:#2171b5,stroke-width:3px,color:#fff
    classDef page fill:#7ed321,stroke:#5cb85c,stroke-width:2px,color:#000
    classDef component fill:#f5a623,stroke:#d68910,stroke-width:2px,color:#000
    classDef subcomponent fill:#bd10e0,stroke:#9013fe,stroke-width:2px,color:#fff

    class APP main
    class HOME,DASH,AUTH,PREVIEW page
    class VOICE,MAP,GLOBE,NAVBAR,BG,PROFILE,AGENT,CONSULT,SHARE,SETTINGS component
    class BANNER,COLORS,VOICE_CONFIG,AI_CONFIG,IFRAME,CONFIG,POSTMSG,OAUTH,CALLBACK,SESSION,TRANSCRIPT,AUDIO,STATUS subcomponent
```

## 🗄️ Database Schema

```mermaid
graph LR
    subgraph "📊 Database Tables"
        ATTORNEYS[👨‍💼 ATTORNEYS<br/>• id (PK)<br/>• subdomain (UK)<br/>• firm_name<br/>• email<br/>• logo_url<br/>• profile_image<br/>• button_image<br/>• colors & styling<br/>• vapi_assistant_id<br/>• vapi_instructions<br/>• practice_areas<br/>• voice_id, ai_model]

        CALLS[📞 CALL_RECORDS<br/>• id (PK)<br/>• attorney_id (FK)<br/>• client_phone<br/>• call_status<br/>• transcript (JSON)<br/>• metadata (JSON)<br/>• started_at<br/>• ended_at<br/>• duration_seconds]

        FIELDS[📝 CUSTOM_FIELDS<br/>• id (PK)<br/>• attorney_id (FK)<br/>• field_name<br/>• field_type<br/>• field_options (JSON)<br/>• prompt_text<br/>• display_order<br/>• is_required]
    end

    ATTORNEYS -->|"1:many"| CALLS
    ATTORNEYS -->|"1:many"| FIELDS

    classDef table fill:#e1f5fe,stroke:#0277bd,stroke-width:2px,color:#000
    class ATTORNEYS,CALLS,FIELDS table
```

## 🔄 Data Flow Diagrams

### 📞 Voice Consultation Flow
```mermaid
sequenceDiagram
    participant C as 👤 Client
    participant S as 🌐 Subdomain
    participant V as 🎤 VapiCall
    participant API as 🔌 API Layer
    participant VAPI as 🎙️ Vapi.ai
    participant DB as 🗄️ Supabase

    C->>S: Visit attorney.legalscout.net
    S->>DB: Load attorney configuration
    DB-->>S: Return attorney data
    S->>V: Initialize voice interface
    C->>V: Click "Start Consultation"
    V->>API: Create call request
    API->>VAPI: Initialize voice call
    VAPI-->>V: Return call session
    V->>C: Start voice conversation
    C->>VAPI: Speak to AI assistant
    VAPI->>VAPI: Process voice → text → AI → text → voice
    VAPI-->>C: AI response (voice)
    VAPI->>API: Send call events (webhook)
    API->>DB: Store call record
    Note over C,DB: Conversation continues...
    VAPI->>API: Call ended event
    API->>DB: Update call record
    API->>C: Send consultation summary
```

### ⚙️ Dashboard Configuration Flow
```mermaid
sequenceDiagram
    participant A as 👨‍💼 Attorney
    participant D as 📊 Dashboard
    participant P as 👁️ Preview
    participant API as 🔌 API Layer
    participant DB as 🗄️ Supabase
    participant VAPI as 🎙️ Vapi.ai

    A->>D: Login via Gmail OAuth
    D->>DB: Fetch attorney profile
    DB-->>D: Return attorney data
    D->>P: Load preview iframe
    A->>D: Upload banner image
    D->>D: handleLogoUpload()
    D->>DB: Save logo_url immediately
    DB-->>D: Confirm save
    D->>P: Update preview (postMessage)
    P->>P: Render updated banner
    A->>D: Click "Remove" banner
    D->>D: handleRemoveLogo()
    D->>DB: Clear logo_url immediately
    DB-->>D: Confirm removal
    D->>P: Update preview
    A->>D: Save other changes
    D->>API: Sync with Vapi assistant
    API->>VAPI: Update assistant config
    VAPI-->>API: Confirm update
```

## 🔧 Recent Improvements

- ✅ **Fixed banner removal persistence issue**: Modified `handleRemoveLogo` and `handleRemoveButtonImage` functions to immediately save changes to Supabase database, ensuring banner removals persist after page refresh
- ✅ **Optimized API routing for Vercel Hobby plan**: Consolidated all API routes into single `api/index.js` file to stay under 12 serverless function limit
- ✅ **Enhanced subdomain detection and handling**: Improved subdomain parsing and attorney profile loading for custom attorney pages
- ✅ **Improved preview system reliability**: Unified preview system using iframe with postMessage communication for consistent behavior
- ✅ **Streamlined authentication flow**: Simplified OAuth callback handling and session management
