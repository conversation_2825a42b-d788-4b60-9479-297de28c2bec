/**
 * Manual Label Check
 * 
 * This script can be run manually in the browser console to check for label issues
 * Usage: Open browser console and type: manualLabelCheck()
 */

window.manualLabelCheck = function() {
  console.clear();
  console.log('🔍 Manual Label Check Starting...');
  
  // Get all labels
  const allLabels = document.querySelectorAll('label');
  const labelsWithFor = document.querySelectorAll('label[for]');
  const labelsWithoutFor = document.querySelectorAll('label:not([for])');
  
  console.log(`📊 Label Statistics:`);
  console.log(`   Total labels: ${allLabels.length}`);
  console.log(`   Labels with 'for': ${labelsWithFor.length}`);
  console.log(`   Labels without 'for': ${labelsWithoutFor.length}`);
  
  // Check each label with 'for' attribute
  console.log('\n🔍 Checking labels with "for" attributes:');
  let issues = [];
  
  labelsWithFor.forEach((label, index) => {
    const forValue = label.getAttribute('for');
    const target = document.getElementById(forValue);
    const labelText = label.textContent.trim();
    
    console.group(`Label ${index + 1}: "${labelText}"`);
    console.log(`for="${forValue}"`);
    console.log(`target exists: ${!!target}`);
    
    if (!target) {
      console.error('❌ NO TARGET FOUND');
      issues.push({
        type: 'missing-target',
        label: label,
        forValue: forValue,
        labelText: labelText
      });
    } else {
      console.log(`target: ${target.tagName}[id="${target.id}"]`);
      if (!['INPUT', 'TEXTAREA', 'SELECT', 'BUTTON'].includes(target.tagName)) {
        console.warn('⚠️ TARGET IS NOT A FORM ELEMENT');
        issues.push({
          type: 'invalid-target',
          label: label,
          forValue: forValue,
          labelText: labelText,
          target: target
        });
      } else {
        console.log('✅ Valid');
      }
    }
    console.groupEnd();
  });
  
  // Check form elements without labels
  console.log('\n🔍 Checking form elements without labels:');
  const formElements = document.querySelectorAll('input, textarea, select');
  
  formElements.forEach((element, index) => {
    if (element.id) {
      const hasExplicitLabel = document.querySelector(`label[for="${element.id}"]`);
      const hasImplicitLabel = element.closest('label');
      
      if (!hasExplicitLabel && !hasImplicitLabel) {
        console.warn(`⚠️ Form element without label: ${element.tagName}[id="${element.id}"][type="${element.type || 'N/A'}"]`);
        issues.push({
          type: 'missing-label',
          element: element,
          elementId: element.id
        });
      }
    }
  });
  
  // Summary
  console.log('\n📋 SUMMARY:');
  if (issues.length === 0) {
    console.log('✅ No label/input issues found!');
  } else {
    console.log(`❌ Found ${issues.length} issues:`);
    issues.forEach((issue, index) => {
      console.log(`   ${index + 1}. ${issue.type}: ${issue.labelText || issue.elementId}`);
    });
    
    console.log('\n🔧 Detailed Issues:');
    issues.forEach((issue, index) => {
      console.group(`Issue ${index + 1}: ${issue.type}`);
      if (issue.type === 'missing-target') {
        console.log('Label:', issue.label);
        console.log('For value:', issue.forValue);
        console.log('Label text:', issue.labelText);
      } else if (issue.type === 'invalid-target') {
        console.log('Label:', issue.label);
        console.log('Target:', issue.target);
        console.log('For value:', issue.forValue);
      } else if (issue.type === 'missing-label') {
        console.log('Element:', issue.element);
        console.log('Element ID:', issue.elementId);
      }
      console.groupEnd();
    });
  }
  
  return {
    totalLabels: allLabels.length,
    labelsWithFor: labelsWithFor.length,
    issues: issues
  };
};

// Auto-run when loaded
setTimeout(() => {
  console.log('🔍 Auto-running manual label check...');
  window.manualLabelCheck();
}, 2000);

console.log('🔍 Manual Label Check loaded. Run manualLabelCheck() to check labels.');
