/**
 * Dashboard Loading Interceptor
 * 
 * Intercepts dashboard loading attempts and redirects them to use
 * the robust state handler instead of the old method that fails
 * for <NAME_EMAIL>
 */

(function dashboardLoadingInterceptor() {
  console.log('🚧 [DashboardLoadingInterceptor] Initializing dashboard loading interception...');
  
  // Intercept early - before other scripts try to load
  let interceptorActive = true;
  
  // Override any existing loadAttorney function immediately
  const originalLoadAttorney = window.loadAttorney;
  
  window.loadAttorney = function(email) {
    console.log(`🚧 [DashboardLoadingInterceptor] Intercepted loadAttorney for: ${email}`);
    
    if (interceptorActive && window.loadAttorneyWithRobustHandling) {
      console.log('🔄 [DashboardLoadingInterceptor] Redirecting to robust handler...');
      return window.loadAttorneyWithRobustHandling(email);
    } else if (originalLoadAttorney) {
      console.log('🔄 [DashboardLoadingInterceptor] Using original method...');
      return originalLoadAttorney(email);
    } else {
      console.log('⚠️ [DashboardLoadingInterceptor] No loading method available, waiting...');
      return new Promise((resolve, reject) => {
        // Wait for robust handler to be available
        const waitInterval = setInterval(() => {
          if (window.loadAttorneyWithRobustHandling) {
            clearInterval(waitInterval);
            console.log('✅ [DashboardLoadingInterceptor] Robust handler now available');
            window.loadAttorneyWithRobustHandling(email).then(resolve).catch(reject);
          }
        }, 100);
        
        // Timeout after 10 seconds
        setTimeout(() => {
          clearInterval(waitInterval);
          reject(new Error('Timeout waiting for robust handler'));
        }, 10000);
      });
    }
  };
  
  // Also intercept any Supabase queries that look for attorneys
  if (window.supabase) {
    interceptSupabaseQueries();
  } else {
    // Wait for Supabase to be available
    const supabaseWatcher = setInterval(() => {
      if (window.supabase) {
        clearInterval(supabaseWatcher);
        interceptSupabaseQueries();
      }
    }, 100);
    
    setTimeout(() => clearInterval(supabaseWatcher), 5000);
  }
  
  function interceptSupabaseQueries() {
    console.log('🚧 [DashboardLoadingInterceptor] Setting up Supabase query interception...');
    
    // Store original from method
    const originalFrom = window.supabase.from;
    
    window.supabase.from = function(table) {
      const query = originalFrom.call(this, table);
      
      // If it's an attorneys query, add our interception
      if (table === 'attorneys') {
        console.log('🚧 [DashboardLoadingInterceptor] Intercepted attorneys query');
        
        // Store original select method
        const originalSelect = query.select;
        
        query.select = function(...args) {
          const selectQuery = originalSelect.apply(this, args);
          
          // Store original eq method
          const originalEq = selectQuery.eq;
          
          selectQuery.eq = function(column, value) {
            if (column === 'email' && interceptorActive) {
              console.log(`🚧 [DashboardLoadingInterceptor] Intercepted email query for: ${value}`);
              
              // Instead of running the query, use our robust handler
              return {
                then: function(callback) {
                  if (window.resolveAttorneyState) {
                    window.resolveAttorneyState(value)
                      .then(state => {
                        if (state.success && state.attorney) {
                          // Return in the format expected by the original code
                          callback({ data: [state.attorney], error: null });
                        } else {
                          // Return empty result to trigger our creation logic
                          callback({ data: [], error: null });
                        }
                      })
                      .catch(error => {
                        console.error('[DashboardLoadingInterceptor] Error in robust handler:', error);
                        callback({ data: [], error: null });
                      });
                  } else {
                    // Robust handler not ready, fall back to original
                    console.log('🔄 [DashboardLoadingInterceptor] Robust handler not ready, using original query');
                    originalEq.call(this, column, value).then(callback);
                  }
                  return this;
                },
                catch: function(errorCallback) {
                  // Handle errors
                  return this;
                }
              };
            } else {
              // Not an email query or interceptor disabled, use original
              return originalEq.call(this, column, value);
            }
          };
          
          return selectQuery;
        };
      }
      
      return query;
    };
    
    console.log('✅ [DashboardLoadingInterceptor] Supabase query interception set up');
  }
  
  // Disable interceptor after robust handler is fully loaded
  const checkRobustHandler = setInterval(() => {
    if (window.loadAttorneyWithRobustHandling && 
        window.resolveAttorneyState && 
        window.createControlledAssistant) {
      console.log('✅ [DashboardLoadingInterceptor] Robust handler fully loaded, keeping interception active');
      // Keep interceptor active to ensure robust handling
      // interceptorActive = false;
      clearInterval(checkRobustHandler);
    }
  }, 500);
  
  // Cleanup after 30 seconds
  setTimeout(() => {
    clearInterval(checkRobustHandler);
    console.log('🚧 [DashboardLoadingInterceptor] Cleanup completed');
  }, 30000);
  
  // Expose control functions
  window.dashboardInterceptor = {
    enable: () => {
      interceptorActive = true;
      console.log('🚧 [DashboardLoadingInterceptor] Interceptor enabled');
    },
    disable: () => {
      interceptorActive = false;
      console.log('🚧 [DashboardLoadingInterceptor] Interceptor disabled');
    },
    isActive: () => interceptorActive
  };
  
  console.log('✅ [DashboardLoadingInterceptor] Dashboard loading interception initialized');
})();
