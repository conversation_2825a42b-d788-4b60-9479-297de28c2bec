<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Clean Authentication Solution</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        #log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            border-radius: 4px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧹 Clean Authentication Solution Test</h1>
        <p>This page tests that all fetch interceptors are removed and Supabase works correctly.</p>

        <div id="results"></div>

        <button onclick="runTests()">Run All Tests</button>
        <button onclick="clearResults()">Clear Results</button>

        <h3>Test Log</h3>
        <div id="log"></div>
    </div>

    <!-- Load Supabase from CDN -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    
    <!-- Load our clean auth solution -->
    <script src="/clean-auth-solution.js"></script>

    <script>
        let resultsElement = document.getElementById('results');
        let logElement = document.getElementById('log');

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] ${message}\n`;
            logElement.textContent += logMessage;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }

        function addResult(test, success, message) {
            const div = document.createElement('div');
            div.className = `test-result ${success ? 'success' : 'error'}`;
            div.innerHTML = `<strong>${test}:</strong> ${success ? '✅' : '❌'} ${message}`;
            resultsElement.appendChild(div);
        }

        function clearResults() {
            resultsElement.innerHTML = '';
            logElement.textContent = '';
        }

        async function testFetchInterceptorRemoval() {
            log('Testing fetch interceptor removal...');
            
            // Check if original fetch is restored
            const isOriginal = window.fetch === window.__ORIGINAL_FETCH__;
            
            if (isOriginal) {
                addResult('Fetch Interceptor Removal', true, 'Original fetch function restored');
                log('✅ Original fetch function restored');
                return true;
            } else {
                addResult('Fetch Interceptor Removal', false, 'Fetch function still wrapped');
                log('❌ Fetch function still wrapped');
                return false;
            }
        }

        async function testSupabaseClient() {
            log('Testing Supabase client...');
            
            if (!window.supabase) {
                addResult('Supabase Client', false, 'Supabase client not available');
                log('❌ Supabase client not available');
                return false;
            }

            try {
                // Test getting session (should not throw)
                const { data: sessionData } = await window.supabase.auth.getSession();
                addResult('Supabase Client', true, 'Client initialized and responsive');
                log('✅ Supabase client working');
                return true;
            } catch (error) {
                addResult('Supabase Client', false, `Error: ${error.message}`);
                log(`❌ Supabase client error: ${error.message}`);
                return false;
            }
        }

        async function testSupabaseQuery() {
            log('Testing Supabase query...');
            
            if (!window.supabase) {
                addResult('Supabase Query', false, 'Supabase client not available');
                return false;
            }

            try {
                // Test a simple query
                const { data, error } = await window.supabase
                    .from('attorneys')
                    .select('id')
                    .limit(1);

                if (error) {
                    addResult('Supabase Query', false, `Query failed: ${error.message}`);
                    log(`❌ Query failed: ${error.message}`);
                    return false;
                } else {
                    addResult('Supabase Query', true, `Query successful, returned ${data.length} results`);
                    log(`✅ Query successful, returned ${data.length} results`);
                    return true;
                }
            } catch (error) {
                addResult('Supabase Query', false, `Query error: ${error.message}`);
                log(`❌ Query error: ${error.message}`);
                return false;
            }
        }

        async function testConflictingScriptsDisabled() {
            log('Testing conflicting scripts disabled...');
            
            const disabledFlags = [
                'window.__FINAL_SUPABASE_FIX_DISABLED',
                'window.__FIX_KEY_CONFUSION_DISABLED',
                'window.__CONSOLIDATED_DASHBOARD_FIX_DISABLED'
            ];

            let allDisabled = true;
            const results = [];

            disabledFlags.forEach(flag => {
                const flagName = flag.split('.')[1];
                const isDisabled = eval(flag);
                results.push(`${flagName}: ${isDisabled ? 'DISABLED' : 'ACTIVE'}`);
                if (!isDisabled) allDisabled = false;
            });

            if (allDisabled) {
                addResult('Conflicting Scripts', true, 'All conflicting scripts disabled');
                log('✅ All conflicting scripts disabled');
                return true;
            } else {
                addResult('Conflicting Scripts', false, `Some scripts still active: ${results.join(', ')}`);
                log(`❌ Some scripts still active: ${results.join(', ')}`);
                return false;
            }
        }

        async function runTests() {
            clearResults();
            log('🧹 Starting clean authentication solution tests...');

            const tests = [
                testFetchInterceptorRemoval,
                testConflictingScriptsDisabled,
                testSupabaseClient,
                testSupabaseQuery
            ];

            let passedTests = 0;
            
            for (const test of tests) {
                try {
                    const result = await test();
                    if (result) passedTests++;
                } catch (error) {
                    log(`❌ Test error: ${error.message}`);
                }
                
                // Small delay between tests
                await new Promise(resolve => setTimeout(resolve, 500));
            }

            log(`\n🏁 Tests completed: ${passedTests}/${tests.length} passed`);
            
            if (passedTests === tests.length) {
                addResult('Overall Result', true, 'All tests passed! Clean auth solution working correctly.');
            } else {
                addResult('Overall Result', false, `${tests.length - passedTests} tests failed. Check the log for details.`);
            }
        }

        // Auto-run tests when page loads
        window.addEventListener('load', () => {
            log('🧹 Clean authentication test page loaded');
            
            // Wait for scripts to initialize
            setTimeout(() => {
                runTests();
            }, 2000);
        });
    </script>
</body>
</html>
