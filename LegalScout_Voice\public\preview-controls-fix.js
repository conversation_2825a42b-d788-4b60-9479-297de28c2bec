/**
 * Preview Controls Fix - Enhanced Version
 *
 * This script ensures that preview controls are visible in both development and production environments.
 * It uses multiple strategies to find the preview iframe and add controls.
 */

(function() {
  console.log('[PreviewControlsFix] Initializing enhanced fix for preview controls visibility');

  // Inject inline CSS for maximum compatibility
  function injectCSS() {
    const style = document.createElement('style');
    style.textContent = `
      /* Universal preview controls that work regardless of DOM structure */
      .universal-preview-controls {
        position: fixed !important;
        top: 80px !important;
        right: 20px !important;
        z-index: 999999 !important;
        display: flex !important;
        flex-direction: column !important;
        gap: 10px !important;
        background-color: rgba(0, 0, 0, 0.6) !important;
        padding: 10px !important;
        border-radius: 8px !important;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
        opacity: 1 !important;
        visibility: visible !important;
        pointer-events: auto !important;
        transition: opacity 0.3s ease !important;
      }

      .universal-preview-controls:hover {
        opacity: 1 !important;
      }

      .universal-preview-controls button {
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        gap: 8px !important;
        padding: 8px 12px !important;
        background-color: rgba(255, 255, 255, 0.9) !important;
        border: 1px solid rgba(0, 0, 0, 0.2) !important;
        border-radius: 4px !important;
        color: #333 !important;
        cursor: pointer !important;
        transition: all 0.2s !important;
        font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
        font-size: 14px !important;
        font-weight: 500 !important;
        line-height: 1.2 !important;
        white-space: nowrap !important;
        text-align: center !important;
        text-decoration: none !important;
        width: 100% !important;
        box-sizing: border-box !important;
      }

      .universal-preview-controls button:hover {
        background-color: rgba(255, 255, 255, 1) !important;
        transform: translateY(-1px) !important;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2) !important;
      }

      .universal-preview-controls button svg {
        width: 16px !important;
        height: 16px !important;
        flex-shrink: 0 !important;
      }

      /* Pulse animation to make controls more noticeable */
      @keyframes pulse-border {
        0% { box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.4); }
        70% { box-shadow: 0 0 0 10px rgba(255, 255, 255, 0); }
        100% { box-shadow: 0 0 0 0 rgba(255, 255, 255, 0); }
      }

      .universal-preview-controls {
        animation: pulse-border 2s infinite;
      }

      /* Make controls less obtrusive after initial display */
      .universal-preview-controls.established {
        opacity: 0.7 !important;
      }

      /* Dark theme support */
      @media (prefers-color-scheme: dark) {
        .universal-preview-controls {
          background-color: rgba(255, 255, 255, 0.2) !important;
        }

        .universal-preview-controls button {
          background-color: rgba(40, 40, 40, 0.9) !important;
          color: rgba(255, 255, 255, 0.9) !important;
          border-color: rgba(255, 255, 255, 0.2) !important;
        }

        .universal-preview-controls button:hover {
          background-color: rgba(60, 60, 60, 1) !important;
        }
      }
    `;
    document.head.appendChild(style);
    console.log('[PreviewControlsFix] CSS injected directly');
  }

  // Find any iframe that might be the preview
  function findPreviewIframe() {
    // Try various selectors that might match the preview iframe
    const selectors = [
      '.preview-iframe',
      'iframe[title="Agent Preview"]',
      'iframe[title="Preview"]',
      'iframe[title*="preview" i]',
      'iframe[src*="preview" i]',
      '.preview-container iframe',
      '.preview-content iframe',
      '.preview-panel iframe',
      // Last resort - any iframe in the document
      'iframe'
    ];

    for (const selector of selectors) {
      const iframe = document.querySelector(selector);
      if (iframe) {
        console.log(`[PreviewControlsFix] Found preview iframe using selector: ${selector}`);
        return iframe;
      }
    }

    console.log('[PreviewControlsFix] No preview iframe found');
    return null;
  }

  // Create universal preview controls that work regardless of DOM structure
  function createUniversalControls() {
    // Check if controls already exist
    if (document.querySelector('.universal-preview-controls')) {
      console.log('[PreviewControlsFix] Universal controls already exist');
      return;
    }

    console.log('[PreviewControlsFix] Creating universal preview controls');

    // Create controls container
    const controls = document.createElement('div');
    controls.className = 'universal-preview-controls';
    controls.id = 'universal-preview-controls';

    // Create refresh button
    const refreshButton = document.createElement('button');
    refreshButton.innerHTML = `
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
        <path d="M21.5 2v6h-6M2.5 22v-6h6M2 11.5a10 10 0 0 1 18.8-4.3M22 12.5a10 10 0 0 1-18.8 4.2"/>
      </svg>
      <span>Refresh Preview</span>
    `;
    refreshButton.addEventListener('click', () => {
      console.log('[PreviewControlsFix] Refresh button clicked');
      const iframe = findPreviewIframe();
      if (iframe) {
        // Store the current src
        const currentSrc = iframe.src;

        // Force a reload by changing the src
        iframe.src = 'about:blank';
        setTimeout(() => {
          iframe.src = currentSrc;
        }, 100);

        console.log('[PreviewControlsFix] Preview refreshed');
      } else {
        console.log('[PreviewControlsFix] No iframe found to refresh');
        alert('Preview iframe not found. Try refreshing the page.');
      }
    });

    // Create fullscreen button
    const fullscreenButton = document.createElement('button');
    fullscreenButton.innerHTML = `
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
        <path d="M15 3h6v6M9 21H3v-6M21 3l-7 7M3 21l7-7"/>
      </svg>
      <span>Toggle Fullscreen</span>
    `;

    // Track fullscreen state
    let isFullscreen = false;

    fullscreenButton.addEventListener('click', () => {
      console.log('[PreviewControlsFix] Fullscreen button clicked');
      const iframe = findPreviewIframe();

      if (!iframe) {
        console.log('[PreviewControlsFix] No iframe found for fullscreen');
        alert('Preview iframe not found. Try refreshing the page.');
        return;
      }

      // Toggle fullscreen state
      isFullscreen = !isFullscreen;

      if (isFullscreen) {
        // Make iframe fullscreen
        const iframeContainer = iframe.parentElement;

        // Store original styles for restoration
        iframe.dataset.originalWidth = iframe.style.width;
        iframe.dataset.originalHeight = iframe.style.height;
        iframe.dataset.originalPosition = iframe.style.position;

        // Apply fullscreen styles
        iframe.style.position = 'fixed';
        iframe.style.top = '0';
        iframe.style.left = '0';
        iframe.style.width = '100vw';
        iframe.style.height = '100vh';
        iframe.style.zIndex = '999998';
        iframe.style.border = 'none';

        // Update button text
        fullscreenButton.innerHTML = `
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M4 14h6v6M20 10h-6V4M14 10l7-7M3 21l7-7"/>
          </svg>
          <span>Exit Fullscreen</span>
        `;

        console.log('[PreviewControlsFix] Entered fullscreen mode');
      } else {
        // Restore original styles
        iframe.style.position = iframe.dataset.originalPosition || '';
        iframe.style.top = '';
        iframe.style.left = '';
        iframe.style.width = iframe.dataset.originalWidth || '';
        iframe.style.height = iframe.dataset.originalHeight || '';
        iframe.style.zIndex = '';

        // Update button text
        fullscreenButton.innerHTML = `
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M15 3h6v6M9 21H3v-6M21 3l-7 7M3 21l7-7"/>
          </svg>
          <span>Toggle Fullscreen</span>
        `;

        console.log('[PreviewControlsFix] Exited fullscreen mode');
      }
    });

    // Add buttons to controls
    controls.appendChild(refreshButton);
    controls.appendChild(fullscreenButton);

    // Add controls to document body
    document.body.appendChild(controls);

    // Make controls less obtrusive after a delay
    setTimeout(() => {
      controls.classList.add('established');
    }, 5000);

    console.log('[PreviewControlsFix] Universal controls created and added to document');
  }

  // Check if user is authenticated and on a page with preview
  function shouldShowControls() {
    // Check if we're on a page that should have preview controls
    const isDashboardPage = window.location.pathname.includes('/dashboard') ||
                           document.querySelector('.dashboard-container') !== null;

    // Check if we're on the agent demo page
    const isAgentDemoPage = window.location.pathname.includes('/demo') ||
                           window.location.pathname.includes('/agent');

    // Check if user is authenticated (multiple methods)
    const isAuthenticated = (
      // Check for auth token in localStorage
      localStorage.getItem('supabase.auth.token') !== null ||
      // Check for attorney data
      localStorage.getItem('attorney') !== null ||
      // Check for user ID
      localStorage.getItem('currentUserId') !== null ||
      // Check for auth state in window object
      (window.auth && window.auth.user) ||
      // Check if we're on a dashboard page (which requires auth)
      isDashboardPage
    );

    // Only show controls on dashboard pages or when authenticated on demo pages
    if (isDashboardPage) {
      console.log('[PreviewControlsFix] On dashboard page, should show controls');
      return true;
    } else if (isAgentDemoPage && isAuthenticated) {
      console.log('[PreviewControlsFix] On agent demo page and authenticated, should show controls');
      return true;
    }

    console.log('[PreviewControlsFix] Not on a page that should show controls');
    return false;
  }

  // Remove controls if they shouldn't be shown
  function removeControlsIfNeeded() {
    const controls = document.querySelector('.universal-preview-controls');
    if (controls && !shouldShowControls()) {
      console.log('[PreviewControlsFix] Removing controls as they should not be shown on this page');
      controls.remove();
    }
  }

  // Initialize the fix
  function init() {
    console.log('[PreviewControlsFix] Initializing...');
    injectCSS();

    // Wait for the DOM to be fully loaded
    if (document.readyState === 'complete' || document.readyState === 'interactive') {
      setTimeout(() => {
        if (shouldShowControls()) {
          createUniversalControls();
        }
      }, 1000);
    } else {
      document.addEventListener('DOMContentLoaded', () => {
        setTimeout(() => {
          if (shouldShowControls()) {
            createUniversalControls();
          }
        }, 1000);
      });
    }

    // Set up a periodic check to ensure the controls exist when they should
    // and don't exist when they shouldn't
    setInterval(() => {
      // First check if controls should be removed
      removeControlsIfNeeded();

      // Then check if controls should be added
      if (shouldShowControls()) {
        // Check if iframe exists but controls don't
        const iframe = findPreviewIframe();
        if (iframe && !document.querySelector('.universal-preview-controls')) {
          console.log('[PreviewControlsFix] Found iframe and should show controls, creating...');
          createUniversalControls();
        }
      }
    }, 5000);

    // Listen for messages from the preview iframe
    window.addEventListener('message', (event) => {
      // Check if the message is from the preview
      if (event.data && (
          event.data === 'PREVIEW_READY' ||
          event.data === 'PREVIEW_PING' ||
          event.data === 'PREVIEW_UPDATE_RECEIVED'
      )) {
        console.log(`[PreviewControlsFix] Received message from preview: ${event.data}`);

        // Create controls if they don't exist and should be shown
        if (shouldShowControls() && !document.querySelector('.universal-preview-controls')) {
          createUniversalControls();
        }
      }
    });

    // Listen for auth state changes
    window.addEventListener('storage', (event) => {
      // Check for auth-related storage changes
      if (event.key && (
          event.key.includes('auth') ||
          event.key === 'attorney' ||
          event.key === 'currentUserId'
      )) {
        console.log(`[PreviewControlsFix] Auth-related storage changed: ${event.key}`);

        // Check if controls should be shown or hidden
        if (shouldShowControls()) {
          if (!document.querySelector('.universal-preview-controls')) {
            createUniversalControls();
          }
        } else {
          removeControlsIfNeeded();
        }
      }
    });

    // Also listen for URL changes (for single-page apps)
    let lastUrl = window.location.href;
    new MutationObserver(() => {
      if (window.location.href !== lastUrl) {
        lastUrl = window.location.href;
        console.log('[PreviewControlsFix] URL changed, checking if controls should be shown');

        // Check if controls should be shown or hidden
        if (shouldShowControls()) {
          if (!document.querySelector('.universal-preview-controls')) {
            createUniversalControls();
          }
        } else {
          removeControlsIfNeeded();
        }
      }
    }).observe(document, {subtree: true, childList: true});
  }

  // Run the initialization
  init();
})();
