/**
 * Enhanced Attorney Manager
 *
 * This script enhances the StandaloneAttorneyManager to:
 * 1. Look up attorneys by email in addition to user_id
 * 2. Create VAPI assistants for attorneys that don't have one
 * 3. Ensure the dashboard always has a valid VAPI assistant ID
 */

(function() {
  console.log('[EnhanceAttorneyManager] Starting enhancement...');

  // Wait for the StandaloneAttorneyManager to be available
  const waitForManager = () => {
    if (window.standaloneAttorneyManager) {
      enhanceManager();
    } else {
      console.log('[EnhanceAttorneyManager] Waiting for StandaloneAttorneyManager...');
      setTimeout(waitForManager, 100);
    }
  };

  // Enhance the StandaloneAttorneyManager
  const enhanceManager = () => {
    console.log('[EnhanceAttorneyManager] Enhancing StandaloneAttorneyManager...');

    // Add a property to store all attorney records
    window.standaloneAttorneyManager.allAttorneys = [];

    // Add flags to prevent cycling
    window.standaloneAttorneyManager.isLoadingByUserId = false;
    window.standaloneAttorneyManager.isLoadingByEmail = false;
    window.standaloneAttorneyManager.hasDispatchedMultipleAttorneysEvent = false;

    // Store the original loadAttorneyForUser method
    const originalLoadAttorneyForUser = window.standaloneAttorneyManager.loadAttorneyForUser;

    // Add a method to switch between attorney records
    window.standaloneAttorneyManager.switchAttorney = function(attorneyId) {
      console.log(`[EnhanceAttorneyManager] Switching to attorney: ${attorneyId}`);

      if (!this.allAttorneys || this.allAttorneys.length === 0) {
        console.error('[EnhanceAttorneyManager] No attorneys available to switch to');
        return false;
      }

      const selectedAttorney = this.allAttorneys.find(a => a.id === attorneyId);

      if (!selectedAttorney) {
        console.error(`[EnhanceAttorneyManager] Attorney with ID ${attorneyId} not found`);
        return false;
      }

      this.attorney = selectedAttorney;
      this.saveToLocalStorage(selectedAttorney);
      this.notifySubscribers();

      return true;
    };

    // Add a new method to load attorney by email
    window.standaloneAttorneyManager.loadAttorneyByEmail = async function(email) {
      console.log(`[EnhanceAttorneyManager] loadAttorneyByEmail called with email: ${email}`);

      // Prevent cycling - if already loading by email, return current attorney
      if (this.isLoadingByEmail) {
        console.log('[EnhanceAttorneyManager] Already loading by email, returning current attorney');
        return this.attorney;
      }

      if (!email) {
        console.error('[EnhanceAttorneyManager] loadAttorneyByEmail: No email provided.');
        this.lastError = new Error('No email provided to loadAttorneyByEmail');
        this.attorney = null;
        this.notifySubscribers();
        throw this.lastError;
      }

      this.isLoading = true;
      this.isLoadingByEmail = true; // Set the email loading flag
      this.lastError = null;
      this.notifySubscribers(); // Notify start of loading

      try {
        // Ensure Supabase client is available
        const supabase = window.supabase;
        if (!supabase) {
          throw new Error('Supabase client not available on window object');
        }

        console.log(`[EnhanceAttorneyManager] Attempting to fetch attorneys by email: ${email}`);

        // Try fetching attorneys by email - get all matching records
        const { data: attorneys, error: fetchError } = await supabase
          .from('attorneys')
          .select('*')
          .eq('email', email)
          .order('updated_at', { ascending: false }); // Order by updated_at to get the most recent first

        if (fetchError) {
          console.error('[EnhanceAttorneyManager] Error fetching attorneys by email:', fetchError);
          throw new Error(`Supabase fetch error: ${fetchError.message}`);
        }

        if (attorneys && attorneys.length > 0) {
          console.log(`[EnhanceAttorneyManager] Found ${attorneys.length} attorneys for email: ${email}`);

          // Filter attorneys to only include those with valid VAPI assistant IDs
          const validAttorneys = attorneys.filter(attorney =>
            attorney.vapi_assistant_id &&
            typeof attorney.vapi_assistant_id === 'string' &&
            attorney.vapi_assistant_id.length > 10 &&
            !attorney.vapi_assistant_id.startsWith('mock-')
          );

          console.log(`[EnhanceAttorneyManager] Found ${validAttorneys.length} attorneys with valid VAPI assistant IDs`);

          // If we have valid attorneys, use those; otherwise, use all attorneys
          const attorneysToUse = validAttorneys.length > 0 ? validAttorneys : attorneys;

          // Store all attorneys
          this.allAttorneys = attorneysToUse;

          // Use the most recently updated attorney by default
          const mostRecentAttorney = attorneysToUse[0];
          console.log(`[EnhanceAttorneyManager] Using most recent attorney: ${mostRecentAttorney.id}`);

          this.attorney = mostRecentAttorney;
          this.saveToLocalStorage(mostRecentAttorney);

          // Dispatch a custom event to notify that multiple attorneys are available
          // Use a debounced approach to prevent multiple rapid events
          if (attorneysToUse.length > 1) {
            console.log(`[EnhanceAttorneyManager] Preparing multipleAttorneysAvailable event with ${attorneysToUse.length} attorneys`);

            // Clear any existing timeout
            if (window.multipleAttorneysEventTimeout) {
              clearTimeout(window.multipleAttorneysEventTimeout);
            }

            // Set a new timeout to dispatch the event after a short delay
            window.multipleAttorneysEventTimeout = setTimeout(() => {
              console.log(`[EnhanceAttorneyManager] Dispatching multipleAttorneysAvailable event with ${attorneysToUse.length} attorneys`);
              const event = new CustomEvent('multipleAttorneysAvailable', {
                detail: {
                  attorneys: attorneysToUse,
                  currentAttorneyId: mostRecentAttorney.id
                }
              });
              window.dispatchEvent(event);
            }, 500); // 500ms delay to debounce multiple events
          }
        } else {
          console.log(`[EnhanceAttorneyManager] No attorney found for email: ${email}`);

          // Create a new attorney record
          const newAttorney = {
            email: email,
            firm_name: 'Your Law Firm',
            subdomain: `attorney-${Math.floor(Math.random() * 100000).toString().padStart(5, '0')}`,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            is_active: true
          };

          console.log(`[EnhanceAttorneyManager] Creating new attorney record:`, newAttorney);

          const { data: createdAttorney, error: createError } = await supabase
            .from('attorneys')
            .insert([newAttorney])
            .select()
            .single();

          if (createError) {
            console.error('[EnhanceAttorneyManager] Error creating attorney:', createError);
            throw new Error(`Error creating attorney: ${createError.message}`);
          }

          console.log(`[EnhanceAttorneyManager] Created new attorney: ${createdAttorney.id}`);
          this.attorney = createdAttorney;
          this.saveToLocalStorage(createdAttorney);
        }

        // Ensure the attorney has a VAPI assistant ID
        await this.ensureVapiAssistant();

        this.isLoading = false;
        this.isLoadingByEmail = false; // Reset the email loading flag
        this.notifySubscribers();
        return this.attorney;
      } catch (error) {
        console.error('[EnhanceAttorneyManager] Error in loadAttorneyByEmail:', error);
        this.lastError = error;
        this.isLoading = false;
        this.isLoadingByEmail = false; // Reset the email loading flag even on error
        this.attorney = null;
        this.notifySubscribers();
        throw error;
      }
    };

    // Add a method to ensure the attorney has a VAPI assistant ID
    window.standaloneAttorneyManager.ensureVapiAssistant = async function() {
      if (!this.attorney) {
        console.error('[EnhanceAttorneyManager] ensureVapiAssistant: No attorney loaded.');
        return;
      }

      // Check if attorney already has a valid VAPI assistant ID
      if (this.attorney.vapi_assistant_id &&
          typeof this.attorney.vapi_assistant_id === 'string' &&
          this.attorney.vapi_assistant_id.length > 10) {
        console.log(`[EnhanceAttorneyManager] Attorney already has VAPI assistant ID: ${this.attorney.vapi_assistant_id}`);
        return;
      }

      // If we have an invalid or empty VAPI assistant ID, log it
      if (this.attorney.vapi_assistant_id) {
        console.warn(`[EnhanceAttorneyManager] Attorney has invalid VAPI assistant ID: ${this.attorney.vapi_assistant_id}, creating new one`);
      }

      console.log('[EnhanceAttorneyManager] Creating VAPI assistant for attorney...');

      try {
        this.isSyncing = true;
        this.notifySubscribers();

        // Check if MCP is available
        if (!window.mcp) {
          console.warn('[EnhanceAttorneyManager] MCP not available, keeping existing assistant ID if available');

          // If attorney already has an assistant ID, keep it
          if (this.attorney.vapi_assistant_id) {
            console.log('[EnhanceAttorneyManager] Attorney already has assistant ID, keeping it:', this.attorney.vapi_assistant_id);
            return this.attorney;
          }

          // Only use fallback if no assistant ID exists
          console.warn('[EnhanceAttorneyManager] No existing assistant ID, using fallback assistant ID');
          const updatedAttorney = {
            ...this.attorney,
            vapi_assistant_id: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', // Default assistant ID
            updated_at: new Date().toISOString()
          };

          // Save to Supabase
          const { error: updateError } = await window.supabase
            .from('attorneys')
            .update({ vapi_assistant_id: updatedAttorney.vapi_assistant_id })
            .eq('id', this.attorney.id);

          if (updateError) {
            console.error('[EnhanceAttorneyManager] Error updating attorney with fallback assistant ID:', updateError);
          }

          // Save to localStorage
          this.saveToLocalStorage(updatedAttorney);

          // Update local state
          this.attorney = updatedAttorney;
          this.isSyncing = false;
          this.notifySubscribers();
          return;
        }

        // Create assistant configuration
        const assistantConfig = {
          name: `${this.attorney.firm_name || 'Your Law Firm'} Legal Assistant`,
          instructions: this.attorney.vapi_instructions ||
            `You are a legal assistant for ${this.attorney.firm_name || 'Your Law Firm'}. Help potential clients understand their legal needs and collect relevant information for consultation.`,
          firstMessage: this.attorney.welcome_message ||
            `Hello, I'm Scout from ${this.attorney.firm_name || 'Your Law Firm'}. How can I help you today?`,
          firstMessageMode: "assistant-speaks-first",
          llm: {
            provider: "openai",
            model: this.attorney.ai_model || "gpt-4o"
          },
          voice: {
            provider: this.attorney.voice_provider || "playht",
            voiceId: this.attorney.voice_id || "sarah"
          },
          transcriber: {
            provider: "deepgram",
            model: "nova-3"
          }
        };

        // Create the assistant using MCP
        const assistant = await window.mcp.invoke('create_assistant_vapi-mcp-server', assistantConfig);

        console.log(`[EnhanceAttorneyManager] Created VAPI assistant: ${assistant.id}`);

        // Update attorney with assistant ID
        const updatedAttorney = {
          ...this.attorney,
          vapi_assistant_id: assistant.id,
          updated_at: new Date().toISOString()
        };

        // Save to Supabase
        const { error: updateError } = await window.supabase
          .from('attorneys')
          .update({ vapi_assistant_id: assistant.id })
          .eq('id', this.attorney.id);

        if (updateError) {
          console.error('[EnhanceAttorneyManager] Error updating attorney with assistant ID:', updateError);
        }

        // Save to localStorage
        this.saveToLocalStorage(updatedAttorney);

        // Update local state
        this.attorney = updatedAttorney;
        this.isSyncing = false;
        this.notifySubscribers();
      } catch (error) {
        console.error('[EnhanceAttorneyManager] Error creating VAPI assistant:', error);
        this.isSyncing = false;
        this.notifySubscribers();
      }
    };

    // Override the loadAttorneyForUser method to handle multiple results and try email as fallback
    window.standaloneAttorneyManager.loadAttorneyForUser = async function(userId) {
      console.log(`[EnhanceAttorneyManager] Enhanced loadAttorneyForUser called with userId: ${userId}`);

      // Prevent cycling - if already loading by user ID, return current attorney
      if (this.isLoadingByUserId) {
        console.log('[EnhanceAttorneyManager] Already loading by user ID, returning current attorney');
        return this.attorney;
      }

      try {
        // Don't use the original method since it doesn't handle multiple results correctly
        // Instead, implement our own query that handles multiple results

        this.isLoading = true;
        this.isLoadingByUserId = true; // Set the user ID loading flag
        this.lastError = null;
        this.notifySubscribers(); // Notify start of loading

        // Ensure Supabase client is available
        const supabase = window.supabase;
        if (!supabase) {
          throw new Error('Supabase client not available on window object');
        }

        console.log(`[EnhanceAttorneyManager] Querying attorneys by user_id: ${userId}`);

        // Use select() without single() to get all matching attorneys
        const { data: attorneys, error: fetchError } = await supabase
          .from('attorneys')
          .select('*')
          .eq('user_id', userId)
          .order('updated_at', { ascending: false }); // Order by updated_at to get the most recent first

        if (fetchError) {
          console.error('[EnhanceAttorneyManager] Error fetching attorneys by user_id:', fetchError);
          throw new Error(`Supabase fetch error: ${fetchError.message}`);
        }

        // Check if we found any attorneys
        if (attorneys && attorneys.length > 0) {
          console.log(`[EnhanceAttorneyManager] Found ${attorneys.length} attorneys for user_id: ${userId}`);

          // Filter attorneys to only include those with valid VAPI assistant IDs
          const validAttorneys = attorneys.filter(attorney =>
            attorney.vapi_assistant_id &&
            typeof attorney.vapi_assistant_id === 'string' &&
            attorney.vapi_assistant_id.length > 10 &&
            !attorney.vapi_assistant_id.startsWith('mock-')
          );

          console.log(`[EnhanceAttorneyManager] Found ${validAttorneys.length} attorneys with valid VAPI assistant IDs`);

          // If we have valid attorneys, use those; otherwise, use all attorneys
          const attorneysToUse = validAttorneys.length > 0 ? validAttorneys : attorneys;

          // Store all attorneys
          this.allAttorneys = attorneysToUse;

          // Use the most recently updated attorney by default
          const mostRecentAttorney = attorneysToUse[0];
          console.log(`[EnhanceAttorneyManager] Using most recent attorney: ${mostRecentAttorney.id}`);

          this.attorney = mostRecentAttorney;
          this.saveToLocalStorage(mostRecentAttorney);

          // Ensure the attorney has a VAPI assistant ID
          await this.ensureVapiAssistant();

          this.isLoading = false;
          this.isLoadingByUserId = false; // Reset the user ID loading flag
          this.notifySubscribers();

          // Dispatch a custom event to notify that multiple attorneys are available
          // Use a debounced approach to prevent multiple rapid events
          if (attorneysToUse.length > 1 && !this.hasDispatchedMultipleAttorneysEvent) {
            console.log(`[EnhanceAttorneyManager] Preparing multipleAttorneysAvailable event with ${attorneysToUse.length} attorneys`);

            // Clear any existing timeout
            if (window.multipleAttorneysEventTimeout) {
              clearTimeout(window.multipleAttorneysEventTimeout);
            }

            // Set a new timeout to dispatch the event after a short delay
            window.multipleAttorneysEventTimeout = setTimeout(() => {
              console.log(`[EnhanceAttorneyManager] Dispatching multipleAttorneysAvailable event with ${attorneysToUse.length} attorneys`);

              // Set the flag to prevent multiple dispatches
              this.hasDispatchedMultipleAttorneysEvent = true;

              const event = new CustomEvent('multipleAttorneysAvailable', {
                detail: {
                  attorneys: attorneysToUse,
                  currentAttorneyId: mostRecentAttorney.id
                }
              });
              window.dispatchEvent(event);

              // Reset the flag after a longer delay to allow for future dispatches if needed
              setTimeout(() => {
                this.hasDispatchedMultipleAttorneysEvent = false;
              }, 5000); // 5 second delay before allowing another dispatch
            }, 500); // 500ms delay to debounce multiple events
          }

          return this.attorney;
        }

        console.log(`[EnhanceAttorneyManager] No attorneys found for user_id: ${userId}`);

        // If we didn't find an attorney by user_id, try to get the user's email
        const { data: { user }, error: userError } = await window.supabase.auth.getUser();

        if (userError) {
          console.error('[EnhanceAttorneyManager] Error getting user:', userError);
          this.isLoadingByUserId = false; // Reset the flag on error
          throw userError;
        }

        if (user && user.email) {
          console.log(`[EnhanceAttorneyManager] Trying to load attorney by email: ${user.email}`);

          // Reset the user ID loading flag before calling loadAttorneyByEmail
          this.isLoadingByUserId = false;

          // Check if we've already dispatched the multiple attorneys event
          // If so, don't try to load by email to prevent cycling
          if (this.hasDispatchedMultipleAttorneysEvent) {
            console.log('[EnhanceAttorneyManager] Multiple attorneys event already dispatched, skipping email lookup');
            this.isLoading = false;
            this.notifySubscribers();
            return this.attorney;
          }

          return await this.loadAttorneyByEmail(user.email);
        }

        this.isLoading = false;
        this.isLoadingByUserId = false; // Reset the user ID loading flag
        this.notifySubscribers();
        return null;
      } catch (error) {
        console.error('[EnhanceAttorneyManager] Error in enhanced loadAttorneyForUser:', error);
        this.isLoading = false;
        this.isLoadingByUserId = false; // Reset the user ID loading flag on error
        this.lastError = error;
        this.attorney = null;
        this.notifySubscribers();
        throw error;
      }
    };

    console.log('[EnhanceAttorneyManager] StandaloneAttorneyManager enhanced successfully');
  };

  // Start the enhancement process
  waitForManager();
})();
