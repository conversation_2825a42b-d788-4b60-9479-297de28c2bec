/**
 * Standalone Attorney Manager
 *
 * A completely standalone implementation of the attorney state manager
 * that doesn't depend on React or any other library.
 *
 * Enhanced with Vapi configuration integration.
 */

(function() {
  console.log('[StandaloneAttorneyManager] Initializing...');

  // UUID validation regex - More lenient to handle various UUID formats
  const UUID_REGEX = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;

  // Storage keys
  const STORAGE_KEYS = {
    ATTORNEY: 'attorney',
    ATTORNEY_ID: 'attorney_id',
    ATTORNEY_VERSION: 'attorney_version',
    LAST_SYNC: 'attorney_last_sync',
    VAPI_CONFIG: 'vapi_config',
    VAPI_CONFIG_TIMESTAMP: 'vapi_config_timestamp'
  };

  // Vapi configuration cache
  let vapiConfigCache = null;

  /**
   * Standalone Attorney Manager
   */
  class StandaloneAttorneyManager {
    constructor() {
      this.attorney = null;
      this.isLoading = false;
      this.isSaving = false;
      this.isSyncing = false;
      this.lastError = null;
      this.subscribers = [];
      this.initialized = false;

      // Initialize immediately - Load Vapi config, but don't load/create attorney yet
      this.initializeVapiConfig();
    }

    /**
     * Initialize Vapi Config only
     */
    async initializeVapiConfig() {
      if (this.initialized) return; // Prevent re-initializing config
      try {
        await this.fetchVapiConfig();
      } catch (configError) {
        console.warn('[StandaloneAttorneyManager] Failed to fetch Vapi configuration during init:', configError);
        vapiConfigCache = this.getDefaultVapiConfig();
      }

      // After Vapi config is loaded, try to load attorney by subdomain if we're on one
      await this.loadAttorneyBySubdomainIfNeeded();
    }

    /**
     * Load attorney by subdomain if we're on an attorney subdomain
     */
    async loadAttorneyBySubdomainIfNeeded() {
      try {
        // Get current subdomain
        const subdomain = this.getCurrentSubdomain();
        console.log('[StandaloneAttorneyManager] Current subdomain:', subdomain);

        // If we're on an attorney subdomain, load the attorney data
        if (subdomain && subdomain !== 'default' && subdomain !== 'www') {
          console.log('[StandaloneAttorneyManager] Loading attorney for subdomain:', subdomain);

          // First try to load from localStorage (much more reliable)
          const localStorageAttorney = this.loadFromLocalStorage();
          if (localStorageAttorney && localStorageAttorney.subdomain === subdomain) {
            console.log('[StandaloneAttorneyManager] Successfully loaded attorney from localStorage for subdomain:', subdomain);
            return;
          }

          // If localStorage doesn't have the right attorney, try Supabase
          console.log('[StandaloneAttorneyManager] localStorage doesn\'t have attorney for subdomain, trying Supabase');
          await this.loadAttorneyBySubdomain(subdomain);
        }
      } catch (error) {
        console.error('[StandaloneAttorneyManager] Error loading attorney by subdomain:', error);
      }
    }

    /**
     * Get current subdomain
     */
    getCurrentSubdomain() {
      if (typeof window === 'undefined') return 'default';

      const host = window.location.hostname;

      // Special cases for localhost or IP addresses
      if (host === 'localhost' || /^\d+\.\d+\.\d+\.\d+$/.test(host)) {
        return 'default';
      }

      // Extract subdomain from hostname
      const parts = host.split('.');

      // Check if we have a subdomain (e.g., damon.legalscout.net)
      if (parts.length > 2 && parts[0] !== 'www') {
        return parts[0];
      }

      return 'default';
    }

    /**
     * Load attorney by subdomain from Supabase
     */
    async loadAttorneyBySubdomain(subdomain) {
      try {
        console.log('[StandaloneAttorneyManager] Loading attorney from Supabase for subdomain:', subdomain);

        // Wait for Supabase client to be available with retry mechanism
        const supabase = await this.waitForSupabase();
        if (!supabase) {
          console.warn('[StandaloneAttorneyManager] Supabase client not available after waiting, skipping subdomain load');
          return;
        }

        // Query attorney by subdomain
        console.log('[StandaloneAttorneyManager] Executing Supabase query for subdomain:', subdomain);
        const { data, error } = await supabase
          .from('attorneys')
          .select('*')
          .eq('subdomain', subdomain)
          .single();

        console.log('[StandaloneAttorneyManager] Supabase query result:', { data, error, subdomain });

        if (error) {
          console.error('[StandaloneAttorneyManager] Error loading attorney by subdomain:', error);
          console.error('[StandaloneAttorneyManager] Error details:', {
            code: error.code,
            message: error.message,
            details: error.details,
            hint: error.hint
          });
          return;
        }

        if (data) {
          console.log('[StandaloneAttorneyManager] Successfully loaded attorney by subdomain:', {
            id: data.id,
            firm_name: data.firm_name,
            subdomain: data.subdomain
          });

          // Debug: Log the full data structure before saving
          console.log('[StandaloneAttorneyManager] Full attorney data from Supabase:', data);
          console.log('[StandaloneAttorneyManager] Data ID check:', {
            hasId: 'id' in data,
            idValue: data.id,
            idType: typeof data.id,
            isValidUUID: this.isValidUUID(data.id)
          });

          this.attorney = data;
          this.saveToLocalStorage(data);
          this.notifySubscribers();
        } else {
          console.warn('[StandaloneAttorneyManager] No attorney found for subdomain:', subdomain);
        }
      } catch (error) {
        console.error('[StandaloneAttorneyManager] Error in loadAttorneyBySubdomain:', error);
      }
    }

    /**
     * Wait for Supabase client to be available
     */
    async waitForSupabase(maxWaitTime = 10000) {
      const startTime = Date.now();

      while (Date.now() - startTime < maxWaitTime) {
        if (window.supabase) {
          console.log('[StandaloneAttorneyManager] Supabase client is now available');
          return window.supabase;
        }

        // Wait 100ms before checking again
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      console.warn('[StandaloneAttorneyManager] Supabase client not available after', maxWaitTime, 'ms');
      return null;
    }

    /**
     * Initialize the manager - DEPRECATED / Simplified - Use loadAttorneyForUser or loadFromLocalStorage directly
     */
    async initialize() {
      console.warn('[StandaloneAttorneyManager] initialize() is deprecated. Use loadAttorneyForUser or loadFromLocalStorage.');
      // We keep this for potential backward compatibility but it does less now.
      await this.initializeVapiConfig();
      this.loadFromLocalStorage(); // Try loading existing data
      this.initialized = true;
      console.log('[StandaloneAttorneyManager] Basic initialization complete. Attorney state depends on subsequent loads.');
      this.notifySubscribers();
    }

    /**
     * Generate a UUID v4
     * @returns {string} A UUID v4 string
     */
    generateUUID() {
      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        const r = Math.random() * 16 | 0;
        const v = c === 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
      });
    }

    /**
     * Validate a UUID
     * @param {string} uuid - The UUID to validate
     * @returns {boolean} Whether the UUID is valid
     */
    isValidUUID(uuid) {
      if (!uuid || typeof uuid !== 'string') {
        console.warn('[StandaloneAttorneyManager] isValidUUID: Invalid input:', uuid);
        return false;
      }

      const isValid = UUID_REGEX.test(uuid);
      if (!isValid) {
        console.warn('[StandaloneAttorneyManager] isValidUUID: UUID failed validation:', uuid);
      }

      return isValid;
    }

    /**
     * Get default Vapi configuration
     * @returns {Object} Default Vapi configuration
     */
    getDefaultVapiConfig() {
      return {
        defaultVoice: {
          provider: '11labs',
          voiceId: 'sarah'
        },
        defaultLLM: {
          provider: 'openai',
          model: 'gpt-4o-mini'
        },
        defaultAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c'
      };
    }

    /**
     * Extract Vapi configuration from assistants
     * @param {Array} assistants - The assistants to extract configuration from
     * @returns {Object} Extracted Vapi configuration
     */
    extractVapiConfig(assistants) {
      // Default configuration
      const config = this.getDefaultVapiConfig();

      // Extract configuration from assistants
      if (assistants && assistants.length > 0) {
        // Use the first assistant as the default
        const defaultAssistant = assistants[0];
        config.defaultAssistantId = defaultAssistant.id;

        // Extract voice configuration
        if (defaultAssistant.voice) {
          config.defaultVoice = {
            provider: defaultAssistant.voice.provider || '11labs',
            voiceId: defaultAssistant.voice.voiceId || 'sarah'
          };
        }

        // Extract LLM configuration
        if (defaultAssistant.llm) {
          config.defaultLLM = {
            provider: defaultAssistant.llm.provider || 'openai',
            model: defaultAssistant.llm.model || 'gpt-4o-mini'
          };
        }
      }

      return config;
    }

    /**
     * Fetch Vapi configuration
     * @returns {Promise<Object>} The Vapi configuration
     */
    async fetchVapiConfig() {
      // Check if we have a cached config that's less than 1 hour old
      const cachedTimestamp = localStorage.getItem(STORAGE_KEYS.VAPI_CONFIG_TIMESTAMP);
      const cachedConfig = localStorage.getItem(STORAGE_KEYS.VAPI_CONFIG);

      if (cachedTimestamp && cachedConfig) {
        const age = Date.now() - parseInt(cachedTimestamp, 10);
        if (age < 60 * 60 * 1000) { // 1 hour
          try {
            const config = JSON.parse(cachedConfig);
            console.log('[StandaloneAttorneyManager] Using cached Vapi configuration');
            vapiConfigCache = config;
            return config;
          } catch (parseError) {
            console.warn('[StandaloneAttorneyManager] Error parsing cached Vapi configuration:', parseError);
          }
        }
      }

      // Check if MCP is available
      if (window.mcp) {
        try {
          console.log('[StandaloneAttorneyManager] Fetching Vapi configuration via MCP');

          // Get assistants via MCP
          const assistants = await window.mcp.invoke('list_assistants_vapi-mcp-server', {});

          // Extract configuration from assistants
          if (assistants && assistants.length > 0) {
            const config = this.extractVapiConfig(assistants);

            // Cache the config
            localStorage.setItem(STORAGE_KEYS.VAPI_CONFIG, JSON.stringify(config));
            localStorage.setItem(STORAGE_KEYS.VAPI_CONFIG_TIMESTAMP, Date.now().toString());

            vapiConfigCache = config;

            return config;
          }
        } catch (mcpError) {
          console.warn('[StandaloneAttorneyManager] Error fetching Vapi configuration via MCP:', mcpError);
        }
      }

      // Fall back to default config
      console.log('[StandaloneAttorneyManager] Using default Vapi configuration');
      const config = this.getDefaultVapiConfig();

      // Cache the config
      localStorage.setItem(STORAGE_KEYS.VAPI_CONFIG, JSON.stringify(config));
      localStorage.setItem(STORAGE_KEYS.VAPI_CONFIG_TIMESTAMP, Date.now().toString());

      vapiConfigCache = config;

      return config;
    }

    /**
     * Create a default attorney object
     * @param {Object} overrides - Properties to override in the default attorney
     * @returns {Object} A default attorney object
     */
    createDefaultAttorney(overrides = {}) {
      // Get default voice from Vapi config
      const defaultVoice = vapiConfigCache?.defaultVoice || { provider: '11labs', voiceId: 'sarah' };

      // Get the current subdomain to determine the correct assistant ID
      const currentSubdomain = this.getCurrentSubdomain();

      // Use the correct assistant ID based on subdomain
      let assistantId = '8d962209-530e-45d2-b2d6-17ed1ef55b3c'; // Default fallback
      if (currentSubdomain === 'damonkost') {
        assistantId = 'f9b97d13-f9c4-40af-a660-62ba5925ff2a'; // Damon's correct assistant
      }

      const defaultAttorney = {
        id: this.generateUUID(),
        subdomain: currentSubdomain || 'default',
        firm_name: currentSubdomain === 'damonkost' ? 'LegalScout' : 'Your Law Firm',
        name: currentSubdomain === 'damonkost' ? 'Damon' : 'Your Name',
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        voice_provider: defaultVoice.provider,
        voice_id: defaultVoice.voiceId,
        welcome_message: currentSubdomain === 'damonkost' ? 'Hello! I\'m your legal assistant. How can I help you today?' : 'Hello! I\'m your legal assistant. How can I help you today?',
        information_gathering_prompt: 'Tell me about your situation, and I\'ll help find the right solution for you.',
        vapi_instructions: currentSubdomain === 'damonkost' ? 'You will guide the user through jurrasic park' : 'You are a legal assistant helping potential clients understand their legal needs and connecting them with appropriate attorneys.',
        vapi_assistant_id: assistantId
      };

      return { ...defaultAttorney, ...overrides };
    }

    /**
     * Load attorney from localStorage
     * @returns {Object|null} The attorney object or null if not found
     */
    loadFromLocalStorage() {
      try {
        // Try to get from localStorage
        const storedAttorney = localStorage.getItem(STORAGE_KEYS.ATTORNEY);
        if (storedAttorney) {
          try {
            const parsedAttorney = JSON.parse(storedAttorney);

            // Validate the attorney data
            if (parsedAttorney && this.isValidUUID(parsedAttorney.id)) {
              console.log('[StandaloneAttorneyManager] Loaded attorney from localStorage:', parsedAttorney.id);

              // Validate and enhance the attorney data
              const validatedAttorney = this.validateAttorneyData(parsedAttorney);

              this.attorney = validatedAttorney;
              this.notifySubscribers();
              return validatedAttorney;
            }
          } catch (parseError) {
            console.error('[StandaloneAttorneyManager] Error parsing attorney from localStorage:', parseError);
          }
        }

        // If not found or invalid, try to get just the ID
        const storedAttorneyId = localStorage.getItem(STORAGE_KEYS.ATTORNEY_ID);
        if (storedAttorneyId && this.isValidUUID(storedAttorneyId)) {
          console.log('[StandaloneAttorneyManager] Found attorney ID in localStorage:', storedAttorneyId);

          // Create a minimal attorney object with the ID
          const minimalAttorney = this.createDefaultAttorney({ id: storedAttorneyId });
          this.attorney = minimalAttorney;
          this.saveToLocalStorage(minimalAttorney);
          this.notifySubscribers();
          return minimalAttorney;
        }

        return null;
      } catch (error) {
        console.error('[StandaloneAttorneyManager] Error loading from localStorage:', error);
        return null;
      }
    }

    /**
     * Save attorney to localStorage
     * @param {Object} attorney - The attorney to save
     */
    saveToLocalStorage(attorney) {
      // If no attorney is provided, use the current attorney
      const attorneyToSave = attorney || this.attorney;

      if (!attorneyToSave || !this.isValidUUID(attorneyToSave.id)) {
        console.warn('[StandaloneAttorneyManager] Cannot save invalid attorney to localStorage');
        return;
      }

      try {
        // Validate the attorney data before saving
        const validatedAttorney = this.validateAttorneyData(attorneyToSave);

        // Save full attorney object
        localStorage.setItem(STORAGE_KEYS.ATTORNEY, JSON.stringify(validatedAttorney));

        // Save ID separately for redundancy
        localStorage.setItem(STORAGE_KEYS.ATTORNEY_ID, validatedAttorney.id);

        // Save version and timestamp
        localStorage.setItem(STORAGE_KEYS.ATTORNEY_VERSION, Date.now().toString());

        console.log('[StandaloneAttorneyManager] Saved attorney to localStorage:', validatedAttorney.id);
      } catch (error) {
        console.error('[StandaloneAttorneyManager] Error saving to localStorage:', error);
      }
    }

    /**
     * Update an existing attorney
     * @param {Object} updates - The attorney data to update
     * @returns {Promise<Object>} The updated attorney
     */
    async updateAttorney(updates) {
      console.log('[StandaloneAttorneyManager] Updating attorney');
      this.isSaving = true;
      this.lastError = null;
      this.notifySubscribers(); // Notify start of save

      try {
        // Ensure we have a valid attorney
        if (!this.attorney || !this.isValidUUID(this.attorney.id)) {
          throw new Error('No valid attorney to update');
        }

        // Merge with existing attorney
        const mergedAttorney = {
          ...this.attorney,
          ...updates,
          updated_at: new Date().toISOString()
        };

        // Ensure ID doesn't change
        mergedAttorney.id = this.attorney.id;

        // Save to localStorage (assume saveToLocalStorage might become async later or involves some async setup)
        // If saveToLocalStorage is purely sync, await won't hurt, but if it becomes async, it's ready.
        await this.saveToLocalStorage(mergedAttorney);
        this.attorney = mergedAttorney; // Update internal state after successful save

        // Check if we need to sync with Vapi
        const needsVapiSync = [
          'firm_name', 'name', 'welcome_message', 'vapi_instructions',
          'voice_provider', 'voice_id', 'vapi_assistant_id' // Ensure assistant ID changes trigger sync
        ].some(key => updates[key] !== undefined && updates[key] !== this.attorney[key]); // Check if relevant field actually changed

        if (needsVapiSync) {
          await this.syncWithVapi(this.attorney); // Await the sync operation
        }

        this.isSaving = false;
        this.notifySubscribers(); // Notify end of save
        return this.attorney; // Return the updated attorney

      } catch (error) {
        console.error('[StandaloneAttorneyManager] Error updating attorney:', error);
        this.lastError = error;
        this.isSaving = false;
        this.notifySubscribers(); // Notify end of save (with error)
        throw error; // Re-throw error so the caller can handle it
      }
    }

    /**
     * Sync attorney with Vapi using MCP
     * @param {Object} attorney - The attorney to sync (defaults to this.attorney)
     * @returns {Promise<Object>} The result of the sync
     */
    async syncWithVapi(attorney = null) {
      try {
        this.isSyncing = true;

        // Use provided attorney or current attorney
        const attorneyToSync = attorney || this.attorney;

        if (!attorneyToSync) {
          throw new Error('No attorney to sync');
        }

        // Check if MCP is available
        if (!window.mcp) {
          console.warn('[StandaloneAttorneyManager] MCP not available, falling back to default assistant');

          // Fall back to using the correct assistant ID based on subdomain
          if (!attorneyToSync.vapi_assistant_id) {
            const currentSubdomain = this.getCurrentSubdomain();
            let assistantId = '8d962209-530e-45d2-b2d6-17ed1ef55b3c'; // Default fallback
            if (currentSubdomain === 'damonkost') {
              assistantId = 'f9b97d13-f9c4-40af-a660-62ba5925ff2a'; // Damon's correct assistant
            }

            const updatedAttorney = {
              ...attorneyToSync,
              vapi_assistant_id: assistantId,
              updated_at: new Date().toISOString()
            };

            // Save to localStorage
            this.saveToLocalStorage(updatedAttorney);

            // Update local state
            this.attorney = updatedAttorney;

            // Notify subscribers
            this.notifySubscribers();
          }

          this.isSyncing = false;
          return { action: 'fallback', error: 'MCP not available' };
        }

        if (!attorneyToSync.vapi_assistant_id) {
          console.log('[StandaloneAttorneyManager] No Vapi assistant ID found - NOT creating automatically');
          console.log('[StandaloneAttorneyManager] Assistant creation must be explicitly requested to prevent overrun');

          // DO NOT automatically create assistants - this was causing the overrun of ~1000 assistants
          // Instead, use the correct assistant ID based on subdomain
          const currentSubdomain = this.getCurrentSubdomain();
          let assistantId = '8d962209-530e-45d2-b2d6-17ed1ef55b3c'; // Default fallback
          if (currentSubdomain === 'damonkost') {
            assistantId = 'f9b97d13-f9c4-40af-a660-62ba5925ff2a'; // Damon's correct assistant
          }

          const updatedAttorney = {
            ...attorneyToSync,
            vapi_assistant_id: assistantId,
            updated_at: new Date().toISOString()
          };

          // Save to localStorage
          this.saveToLocalStorage(updatedAttorney);

          // Update local state
          this.attorney = updatedAttorney;

          // Notify subscribers
          this.notifySubscribers();

          this.isSyncing = false;
          return { action: 'default_assigned', assistant_id: assistantId };

          // COMMENTED OUT: Automatic assistant creation code that was causing the overrun
          /*
          try {
            // Create a new assistant using MCP
            const assistantData = {
              name: `${attorneyToSync.name}'s Legal Assistant`,
              instructions: attorneyToSync.vapi_instructions || `You are a legal assistant for ${attorneyToSync.firm_name || 'a law firm'}. Help potential clients understand their legal needs and collect relevant information for consultation.`,
              firstMessage: attorneyToSync.welcome_message || 'Hello! I\'m your legal assistant. How can I help you today?',
              voice: {
                provider: attorneyToSync.voice_provider || '11labs',
                voiceId: attorneyToSync.voice_id || 'sarah'
              },
              llm: {
                provider: 'openai',
                model: 'gpt-4o-mini'
              }
            };

            // Call MCP to create assistant
            const newAssistant = await window.mcp.invoke('create_assistant_vapi-mcp-server', assistantData);

            // Update the attorney with the new assistant ID
            const updatedAttorney = {
              ...attorneyToSync,
              vapi_assistant_id: newAssistant.id,
              updated_at: new Date().toISOString()
            };

            // Save to localStorage
            this.saveToLocalStorage(updatedAttorney);

            // Update local state
            this.attorney = updatedAttorney;

            // Notify subscribers
            this.notifySubscribers();

            // Also save to Supabase if we have a user ID
            if (updatedAttorney.user_id) {
              this.saveAssistantMappingToSupabase(updatedAttorney.id, newAssistant.id, updatedAttorney.user_id)
                .catch(error => {
                  console.error('[StandaloneAttorneyManager] Error saving assistant mapping to Supabase:', error);
                });
            }

            console.log('[StandaloneAttorneyManager] Created new Vapi assistant via MCP:', newAssistant.id);

            this.isSyncing = false;
            return { action: 'created', assistant: newAssistant };
          } catch (createError) {
            console.error('[StandaloneAttorneyManager] Error creating Vapi assistant via MCP:', createError);

            // Fall back to using a default assistant ID
            const updatedAttorney = {
              ...attorneyToSync,
              vapi_assistant_id: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', // Default assistant ID
              updated_at: new Date().toISOString()
            };

            // Save to localStorage
            this.saveToLocalStorage(updatedAttorney);

            // Update local state
            this.attorney = updatedAttorney;

            // Notify subscribers
            this.notifySubscribers();

            this.isSyncing = false;
            return { action: 'fallback', error: createError.message };
          }
          */
        } else {
          console.log('[StandaloneAttorneyManager] Updating existing Vapi assistant via MCP:', attorneyToSync.vapi_assistant_id);

          try {
            // First get the current assistant to check if it exists
            const existingAssistant = await window.mcp.invoke('get_assistant_vapi-mcp-server', {
              assistantId: attorneyToSync.vapi_assistant_id
            }).catch(() => null);

            // If assistant doesn't exist, use the correct assistant ID based on subdomain
            if (!existingAssistant) {
              console.log('[StandaloneAttorneyManager] Assistant not found, using correct assistant for subdomain instead of creating new one');

              const currentSubdomain = this.getCurrentSubdomain();
              let assistantId = '8d962209-530e-45d2-b2d6-17ed1ef55b3c'; // Default fallback
              if (currentSubdomain === 'damonkost') {
                assistantId = 'f9b97d13-f9c4-40af-a660-62ba5925ff2a'; // Damon's correct assistant
              }

              const updatedAttorney = {
                ...attorneyToSync,
                vapi_assistant_id: assistantId,
                updated_at: new Date().toISOString()
              };

              // Save to localStorage
              this.saveToLocalStorage(updatedAttorney);

              // Update local state
              this.attorney = updatedAttorney;

              // Notify subscribers
              this.notifySubscribers();

              this.isSyncing = false;
              return { action: 'default_fallback', assistant_id: assistantId };
            }

            // For now, we can't update assistants via MCP, so we'll just return the existing one
            // In the future, when MCP supports updating assistants, we can implement that here

            console.log('[StandaloneAttorneyManager] Using existing Vapi assistant:', existingAssistant.id);

            this.isSyncing = false;
            return { action: 'existing', assistant: existingAssistant };
          } catch (updateError) {
            console.error('[StandaloneAttorneyManager] Error updating Vapi assistant via MCP:', updateError);
            this.isSyncing = false;
            return { action: 'failed', error: updateError.message };
          }
        }
      } catch (error) {
        console.error('[StandaloneAttorneyManager] Error syncing with Vapi:', error);
        this.lastError = error;
        this.isSyncing = false;
        return { action: 'error', error: error.message };
      }
    }

    /**
     * Explicitly create a new Vapi assistant (for dashboard use)
     * This method should only be called when the user explicitly requests assistant creation
     * @param {Object} attorneyData - The attorney data to create assistant for
     * @returns {Promise<Object>} The created assistant
     */
    async createVapiAssistantExplicitly(attorneyData = null) {
      const attorneyToUse = attorneyData || this.attorney;

      if (!attorneyToUse) {
        throw new Error('No attorney data available for assistant creation');
      }

      console.log('[StandaloneAttorneyManager] Explicitly creating new Vapi assistant via MCP');

      // Check if MCP is available
      if (!window.mcp || typeof window.mcp.invoke !== 'function') {
        throw new Error('MCP not available for assistant creation');
      }

      try {
        // Create a new assistant using MCP
        const assistantData = {
          name: `${attorneyToUse.name}'s Legal Assistant`,
          instructions: attorneyToUse.vapi_instructions || `You are a legal assistant for ${attorneyToUse.firm_name || 'a law firm'}. Help potential clients understand their legal needs and collect relevant information for consultation.`,
          firstMessage: attorneyToUse.welcome_message || 'Hello! I\'m your legal assistant. How can I help you today?',
          voice: {
            provider: attorneyToUse.voice_provider || '11labs',
            voiceId: attorneyToUse.voice_id || 'sarah'
          },
          llm: {
            provider: 'openai',
            model: 'gpt-4o-mini'
          }
        };

        // Call MCP to create assistant
        const newAssistant = await window.mcp.invoke('create_assistant_vapi-mcp-server', assistantData);

        // Update the attorney with the new assistant ID
        const updatedAttorney = {
          ...attorneyToUse,
          vapi_assistant_id: newAssistant.id,
          updated_at: new Date().toISOString()
        };

        // Save to localStorage
        this.saveToLocalStorage(updatedAttorney);

        // Update local state
        this.attorney = updatedAttorney;

        // Notify subscribers
        this.notifySubscribers();

        // Also save to Supabase if we have a user ID
        if (updatedAttorney.user_id) {
          this.saveAssistantMappingToSupabase(updatedAttorney.id, newAssistant.id, updatedAttorney.user_id)
            .catch(error => {
              console.error('[StandaloneAttorneyManager] Error saving assistant mapping to Supabase:', error);
            });
        }

        console.log('[StandaloneAttorneyManager] Successfully created new Vapi assistant via MCP:', newAssistant.id);
        return { action: 'created', assistant: newAssistant };
      } catch (createError) {
        console.error('[StandaloneAttorneyManager] Error creating Vapi assistant via MCP:', createError);
        throw createError;
      }
    }

    /**
     * Save assistant mapping to Supabase
     * @param {string} attorneyId - The attorney ID
     * @param {string} assistantId - The Vapi assistant ID
     * @param {string} userId - The user ID
     * @returns {Promise<Object>} The result of the operation
     */
    async saveAssistantMappingToSupabase(attorneyId, assistantId, userId) {
      try {
        // Check if Supabase is available
        if (!window.supabase) {
          console.warn('[StandaloneAttorneyManager] Supabase not available, skipping assistant mapping save');
          return { success: false, error: 'Supabase not available' };
        }

        // Save the mapping to Supabase
        const { data, error } = await window.supabase
          .from('attorney_assistants')
          .upsert({
            attorney_id: attorneyId,
            assistant_id: assistantId,
            user_id: userId,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }, {
            onConflict: 'attorney_id',
            returning: 'minimal'
          });

        if (error) {
          throw error;
        }

        return { success: true, data };
      } catch (error) {
        console.error('[StandaloneAttorneyManager] Error saving assistant mapping to Supabase:', error);
        return { success: false, error: error.message };
      }
    }

    /**
     * Validate attorney data and ensure all required fields are present
     * @param {Object} attorneyData - The attorney data to validate
     * @returns {Object} The validated and enhanced attorney data
     */
    validateAttorneyData(attorneyData) {
      if (!attorneyData) {
        console.warn('[StandaloneAttorneyManager] validateAttorneyData: No attorney data provided');
        return this.createDefaultAttorney();
      }

      // Debug logging to understand the data structure
      console.log('[StandaloneAttorneyManager] validateAttorneyData: Received data:', {
        hasId: 'id' in attorneyData,
        idValue: attorneyData.id,
        idType: typeof attorneyData.id,
        keys: Object.keys(attorneyData)
      });

      // Check if ID is valid
      if (!this.isValidUUID(attorneyData.id)) {
        console.warn('[StandaloneAttorneyManager] validateAttorneyData: Invalid attorney ID, creating new one');
        return this.createDefaultAttorney();
      }

      // Get default voice from Vapi config
      const defaultVoice = vapiConfigCache?.defaultVoice || { provider: '11labs', voiceId: 'sarah' };

      // Get the current subdomain to determine the correct assistant ID
      const currentSubdomain = this.getCurrentSubdomain();

      // Use the correct assistant ID based on subdomain
      let assistantId = '8d962209-530e-45d2-b2d6-17ed1ef55b3c'; // Default fallback
      if (currentSubdomain === 'damonkost') {
        assistantId = 'f9b97d13-f9c4-40af-a660-62ba5925ff2a'; // Damon's correct assistant
      }

      // Create a default attorney to use as a base for missing fields
      const defaultAttorney = {
        id: attorneyData.id, // Keep the original ID
        subdomain: currentSubdomain || attorneyData.subdomain || 'default',
        firm_name: currentSubdomain === 'damonkost' ? 'LegalScout' : (attorneyData.firm_name || 'Your Law Firm'),
        name: currentSubdomain === 'damonkost' ? 'Damon' : (attorneyData.name || 'Your Name'),
        is_active: true,
        created_at: attorneyData.created_at || new Date().toISOString(),
        updated_at: new Date().toISOString(),
        voice_provider: defaultVoice.provider,
        voice_id: defaultVoice.voiceId,
        welcome_message: 'Hello! I\'m your legal assistant. How can I help you today?',
        information_gathering_prompt: 'Tell me about your situation, and I\'ll help find the right solution for you.',
        vapi_instructions: currentSubdomain === 'damonkost' ? 'You will guide the user through jurrasic park' : 'You are a legal assistant helping potential clients understand their legal needs and connecting them with appropriate attorneys.',
        vapi_assistant_id: assistantId
      };

      // Merge the attorney data with the default attorney
      const validatedAttorney = {
        ...defaultAttorney,
        ...attorneyData,
        updated_at: new Date().toISOString() // Always update the timestamp
      };

      // Ensure ID doesn't change
      validatedAttorney.id = attorneyData.id;

      // Ensure the correct assistant ID is used for the subdomain
      if (currentSubdomain === 'damonkost') {
        validatedAttorney.vapi_assistant_id = 'f9b97d13-f9c4-40af-a660-62ba5925ff2a';
        validatedAttorney.firm_name = 'LegalScout';
        validatedAttorney.vapi_instructions = 'You will guide the user through jurrasic park';
      }

      console.log('[StandaloneAttorneyManager] Validated attorney data:', validatedAttorney.id, 'with assistant:', validatedAttorney.vapi_assistant_id);

      return validatedAttorney;
    }

    /**
     * Subscribe to attorney state changes
     * @param {Function} callback - The callback to call when state changes
     * @returns {Function} A function to unsubscribe
     */
    subscribe(callback) {
      if (typeof callback !== 'function') {
        console.warn('[StandaloneAttorneyManager] Cannot subscribe with non-function callback');
        return () => {};
      }

      this.subscribers.push(callback);

      // Call immediately with current state
      if (this.attorney) {
        callback(this.attorney);
      }

      // Return unsubscribe function
      return () => this.unsubscribe(callback);
    }

    /**
     * Unsubscribe from attorney state changes
     * @param {Function} callback - The callback to unsubscribe
     */
    unsubscribe(callback) {
      this.subscribers = this.subscribers.filter(cb => cb !== callback);
    }

    /**
     * Notify all subscribers of state changes
     */
    notifySubscribers() {
      if (!this.attorney) return;

      this.subscribers.forEach(callback => {
        try {
          callback(this.attorney);
        } catch (error) {
          console.error('[StandaloneAttorneyManager] Error in subscriber callback:', error);
        }
      });
    }

    /**
     * Load attorney data for a specific user ID from Supabase
     * Creates a new record if one doesn't exist.
     * @param {string} userId - The Supabase auth user ID
     * @returns {Promise<Object>} The loaded or created attorney object
     */
    async loadAttorneyForUser(userId) {
      console.log(`[StandaloneAttorneyManager] loadAttorneyForUser called with userId: ${userId}`); // Log entry

      if (!userId) {
        console.error('[StandaloneAttorneyManager] loadAttorneyForUser: No userId provided.');
        this.lastError = new Error('No userId provided to loadAttorneyForUser');
        this.attorney = null; // Ensure attorney state is null
        this.notifySubscribers();
        throw this.lastError;
      }

      // Validate UUID format for userId
      if (!this.isValidUUID(userId)) {
        console.error(`[StandaloneAttorneyManager] loadAttorneyForUser: Invalid userId format: ${userId}`);
        this.lastError = new Error('Invalid userId format provided');
        this.attorney = null;
        this.notifySubscribers();
        throw this.lastError;
      }

      this.isLoading = true;
      this.lastError = null;
      this.notifySubscribers(); // Notify start of loading

      try {
        // Ensure Supabase client is available - Use window.supabase directly
        const supabase = window.supabase;
        if (!supabase) {
          throw new Error('Supabase client not available on window object'); // Update error message
        }

        console.log(`[StandaloneAttorneyManager] Attempting to fetch attorney by user_id: ${userId}`);
        // Try fetching attorney by user_id
        const { data: existingAttorney, error: fetchError } = await supabase
          .from('attorneys')
          .select('*')
          .eq('user_id', userId)
          .maybeSingle(); // Use maybeSingle to handle 0 or 1 result gracefully

        if (fetchError) {
          console.error('[StandaloneAttorneyManager] Error fetching attorney by user_id:', fetchError);
          throw new Error(`Supabase fetch error: ${fetchError.message}`);
        }

        if (existingAttorney) {
          console.log('[StandaloneAttorneyManager] Found existing attorney by user_id:', existingAttorney.id);
          this.attorney = this.validateAttorneyData(existingAttorney);
          this.saveToLocalStorage(); // Save valid attorney
        } else {
          console.log(`[StandaloneAttorneyManager] No existing attorney found for user_id ${userId}. Attempting to create a new one.`);

          // Need user's email to create - how do we get this reliably here?
          // This function might need access to the full user object, not just ID,
          // or the calling context needs to ensure creation happens elsewhere if email is needed.
          // For now, we'll throw an error indicating creation isn't supported here directly without more info.

          // TEMPORARY: Let's try fetching user details if possible (might require different Supabase setup/permissions)
          let userEmail = null;
          let userName = null;
          try {
             console.log(`[StandaloneAttorneyManager] Attempting to fetch user data for ID: ${userId}`);
             // This requires admin client or specific policy setup - MIGHT FAIL
             // const { data: userData, error: userError } = await supabase.auth.admin.getUserById(userId);
             // SAFER Alternative: Assume email might be available via auth context elsewhere?
             // For now, just log that we can't create without more info.
             console.warn('[StandaloneAttorneyManager] Cannot reliably get user email/name from just userId inside the manager. Attorney creation should ideally be handled by AuthContext or passed in.');

          } catch (getUserError) {
             console.error('[StandaloneAttorneyManager] Error trying to fetch user details:', getUserError);
          }

          // If we had email/name, creation would look like this:
          /*
          const { data: newAttorney, error: createError } = await supabase
            .from('attorneys')
            .insert({
              user_id: userId,
              email: userEmail || `placeholder-${userId}@example.com`, // Placeholder if email unavailable
              name: userName || 'New Attorney',
              // Add other minimal default fields if necessary
            })
            .select()
            .single();

          if (createError) {
            console.error('[StandaloneAttorneyManager] Error creating new attorney:', createError);
            throw new Error(`Supabase create error: ${createError.message}`);
          }

          console.log('[StandaloneAttorneyManager] Successfully created new attorney:', newAttorney.id);
          this.attorney = this.validateAttorneyData(newAttorney);
          this.saveToLocalStorage(); // Save newly created attorney
          */

          // Since we can't reliably create here, set attorney to null
          console.warn('[StandaloneAttorneyManager] Skipping attorney creation due to missing user details.');
          this.attorney = null;
          // Optionally set an error state? Or let the UI handle the null attorney state?
          // For now, just resolve with null attorney.
          // throw new Error('New attorney creation from userId alone is not fully supported here.');

        }

        this.isLoading = false;
        this.notifySubscribers(); // Notify successful load/attempt
        console.log('[StandaloneAttorneyManager] loadAttorneyForUser completed. Attorney state:', this.attorney ? this.attorney.id : null);
        return this.attorney;

      } catch (error) {
        // Log the full error object for better debugging
        console.error('[StandaloneAttorneyManager] FATAL error in loadAttorneyForUser:', error);
        this.lastError = error;
        this.isLoading = false;
        this.attorney = null; // Ensure attorney is null on error
        this.notifySubscribers(); // Notify error state
        throw error; // Re-throw the error
      }
    }
  }

  // Create global instance
  window.standaloneAttorneyManager = new StandaloneAttorneyManager();

  console.log('[StandaloneAttorneyManager] Global instance created');
})();
