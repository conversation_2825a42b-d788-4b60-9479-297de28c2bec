<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Three.js Simple Test</title>
    <!-- Load Three.js from cdnjs -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
</head>
<body>
    <div id="status">Loading...</div>
    
    <script>
        // Status reporting
        function updateStatus(message) {
            document.getElementById('status').innerHTML += '<br>' + message;
            console.log(message);
        }
        
        // Wait for everything to load
        window.onload = function() {
            updateStatus('Window loaded');
            
            // Check if THREE is defined
            updateStatus('THREE defined: ' + (typeof THREE !== 'undefined'));
            
            // Try to create a simple scene
            try {
                const scene = new THREE.Scene();
                updateStatus('Scene created successfully');
                
                // Create a camera
                const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
                camera.position.z = 5;
                updateStatus('Camera created successfully');
                
                // Create a renderer
                const renderer = new THREE.WebGLRenderer();
                renderer.setSize(window.innerWidth, window.innerHeight);
                document.body.appendChild(renderer.domElement);
                updateStatus('Renderer created successfully');
                
                // Create a cube
                const geometry = new THREE.BoxGeometry();
                const material = new THREE.MeshBasicMaterial({ color: 0x00ff00 });
                const cube = new THREE.Mesh(geometry, material);
                scene.add(cube);
                updateStatus('Cube created and added to scene');
                
                // Animation function
                function animate() {
                    requestAnimationFrame(animate);
                    
                    cube.rotation.x += 0.01;
                    cube.rotation.y += 0.01;
                    
                    renderer.render(scene, camera);
                }
                
                // Start animation
                animate();
                updateStatus('Animation started');
            } catch (error) {
                updateStatus('Error: ' + error);
            }
        };
    </script>
</body>
</html>
