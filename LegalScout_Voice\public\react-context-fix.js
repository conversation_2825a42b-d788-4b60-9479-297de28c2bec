/**
 * Aggressive React Context Fix
 *
 * This script fixes issues with React.createContext by ensuring it's properly defined
 * in the global React object. It's loaded early in the page to prevent errors.
 */

(function() {
  console.log('[ReactContextFix] Starting aggressive fix...');

  // Create a global React object if it doesn't exist
  if (typeof window.React === 'undefined') {
    window.React = {};
    console.log('[ReactContextFix] Created global React object');
  }

  // Immediately add createContext to the global React object
  if (typeof window.React.createContext === 'undefined') {
    window.React.createContext = function createContext(defaultValue) {
      console.log('[ReactContextFix] Creating context with default value:', defaultValue);

      const context = {
        Provider: function Provider(props) {
          console.log('[ReactContextFix] Provider called with props:', props);
          context._currentValue = props.value !== undefined ? props.value : defaultValue;
          return props.children;
        },
        Consumer: function Consumer(props) {
          console.log('[ReactContextFix] Consumer called with props:', props);
          return props.children(context._currentValue);
        },
        _currentValue: defaultValue,
        // Add these properties to make it more compatible with React's implementation
        $$typeof: Symbol.for('react.context'),
        _currentRenderer: null,
        _defaultValue: defaultValue
      };

      return context;
    };

    console.log('[ReactContextFix] Added createContext polyfill to global React');
  }

  // Continue to monitor and ensure React.createContext exists
  function monitorReactContext() {
    // If React exists but createContext doesn't, add it again
    if (window.React && typeof window.React.createContext === 'undefined') {
      console.log('[ReactContextFix] React exists but createContext missing, re-adding');

      window.React.createContext = function createContext(defaultValue) {
        const context = {
          Provider: function Provider(props) {
            context._currentValue = props.value !== undefined ? props.value : defaultValue;
            return props.children;
          },
          Consumer: function Consumer(props) {
            return props.children(context._currentValue);
          },
          _currentValue: defaultValue,
          $$typeof: Symbol.for('react.context'),
          _currentRenderer: null,
          _defaultValue: defaultValue
        };
        return context;
      };
    }

    // Continue monitoring
    setTimeout(monitorReactContext, 100);
  }

  // Start monitoring
  monitorReactContext();

  // Also patch the module system to ensure createContext is available
  if (typeof window.__REACT_CONTEXT_PATCH__ === 'undefined') {
    window.__REACT_CONTEXT_PATCH__ = true;

    // Save the original createElement function if it exists
    const originalCreateElement = window.React.createElement;

    // Override createElement to ensure it exists and works properly
    window.React.createElement = function(type, props, ...children) {
      // If the original createElement exists, use it
      if (originalCreateElement) {
        return originalCreateElement(type, props, ...children);
      }

      // Simple implementation for when the original doesn't exist
      const element = {
        type: type,
        props: props ? { ...props, children: children.length === 1 ? children[0] : children } : { children: children.length === 1 ? children[0] : children },
        key: props ? props.key : undefined,
        ref: props ? props.ref : undefined
      };

      return element;
    };

    console.log('[ReactContextFix] Patched React.createElement');
  }
})();
