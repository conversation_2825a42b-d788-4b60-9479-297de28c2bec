/**
 * Direct Patch for LayoutGroupContext.mjs
 * 
 * This script specifically targets the LayoutGroupContext.mjs file by
 * defining a global LayoutGroupContext object and intercepting module imports.
 */

(function() {
  console.log('[LayoutGroupPatch] Setting up direct LayoutGroupContext patch');

  // Define a global LayoutGroupContext object
  window.LayoutGroupContext = {
    Provider: function(props) { return props.children || null; },
    Consumer: function(props) { return props.children ? props.children({}) : null; },
    displayName: 'LayoutGroupContext'
  };

  // Create a mock module for LayoutGroupContext.mjs
  const mockModule = {
    LayoutGroupContext: window.LayoutGroupContext,
    default: window.LayoutGroupContext
  };

  // Override import.meta to prevent errors
  if (typeof window.import !== 'undefined' && typeof window.import.meta === 'undefined') {
    window.import.meta = { url: window.location.href };
  }

  // Define a global __vite__resolveUrl function if it doesn't exist
  if (typeof window.__vite__resolveUrl === 'undefined') {
    window.__vite__resolveUrl = function(url) { return url; };
  }

  // Intercept dynamic imports for LayoutGroupContext.mjs
  const originalImport = window.import || function() { return Promise.reject(new Error('import not supported')); };
  window.import = function(specifier) {
    if (specifier && typeof specifier === 'string' && 
        (specifier.includes('LayoutGroupContext.mjs') || 
         specifier.includes('LayoutGroupContext'))) {
      console.log('[LayoutGroupPatch] Intercepted import for:', specifier);
      return Promise.resolve(mockModule);
    }
    return originalImport.apply(this, arguments);
  };

  // Create a MutationObserver to watch for script tags loading LayoutGroupContext.mjs
  const observer = new MutationObserver(function(mutations) {
    mutations.forEach(function(mutation) {
      if (mutation.type === 'childList') {
        mutation.addedNodes.forEach(function(node) {
          if (node.tagName === 'SCRIPT' && 
              node.src && 
              node.src.includes('LayoutGroupContext')) {
            console.log('[LayoutGroupPatch] Blocking script:', node.src);
            node.src = '';
            node.textContent = `
              // Mock implementation
              const LayoutGroupContext = window.LayoutGroupContext;
              export { LayoutGroupContext };
              export default LayoutGroupContext;
            `;
          }
        });
      }
    });
  });

  // Start observing the document
  observer.observe(document, { childList: true, subtree: true });

  // Define the LayoutGroupContext module in the global scope
  window.framerMotionLayoutGroupContext = mockModule;

  console.log('[LayoutGroupPatch] Direct LayoutGroupContext patch applied');
})();
