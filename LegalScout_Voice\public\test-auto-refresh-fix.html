<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Auto-Refresh Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .timer {
            font-size: 24px;
            font-weight: bold;
            text-align: center;
            margin: 20px 0;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 8px;
        }
        .counter {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin: 20px 0;
        }
        .counter-item {
            text-align: center;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }
        .counter-value {
            font-size: 32px;
            font-weight: bold;
            color: #007bff;
        }
        .counter-label {
            font-size: 14px;
            color: #6c757d;
            margin-top: 5px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚫 Auto-Refresh Fix Test</h1>
        <p>This page tests that auto-refresh mechanisms have been disabled. The page should remain stable without any automatic reloads.</p>
        
        <div class="timer">
            ⏱️ Page Uptime: <span id="uptime">0</span> seconds
        </div>
        
        <div class="counter">
            <div class="counter-item">
                <div class="counter-value" id="reload-attempts">0</div>
                <div class="counter-label">Blocked Reload Attempts</div>
            </div>
            <div class="counter-item">
                <div class="counter-value" id="interval-count">0</div>
                <div class="counter-label">Active Intervals</div>
            </div>
            <div class="counter-item">
                <div class="counter-value" id="timeout-count">0</div>
                <div class="counter-label">Blocked Auto-Refresh Timeouts</div>
            </div>
        </div>
        
        <div id="status-container">
            <div class="status info">
                <span id="status-text">🔄 Monitoring for auto-refresh attempts...</span>
            </div>
        </div>
        
        <div>
            <button onclick="testReload()">🧪 Test Reload Block</button>
            <button onclick="testTimeout()">🧪 Test Timeout Block</button>
            <button onclick="clearLog()">🧹 Clear Log</button>
            <button onclick="manualRefresh()">🔄 Manual Refresh (Allowed)</button>
        </div>
        
        <h3>📝 Activity Log</h3>
        <div id="log" class="log"></div>
    </div>

    <!-- Load our error loop fix first -->
    <script src="/error-loop-fix.js"></script>
    
    <script>
        let startTime = Date.now();
        let reloadAttempts = 0;
        let timeoutBlocks = 0;
        let logElement = document.getElementById('log');
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const entry = `[${timestamp}] ${message}\n`;
            logElement.textContent += entry;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }
        
        function clearLog() {
            logElement.textContent = '';
        }
        
        function updateCounters() {
            const uptime = Math.floor((Date.now() - startTime) / 1000);
            document.getElementById('uptime').textContent = uptime;
            document.getElementById('reload-attempts').textContent = reloadAttempts;
            document.getElementById('timeout-count').textContent = timeoutBlocks;
            
            // Count active intervals (approximate)
            let intervalCount = 0;
            try {
                // This is a rough estimate
                const testInterval = setInterval(() => {}, 1000000);
                intervalCount = testInterval;
                clearInterval(testInterval);
            } catch (e) {
                intervalCount = 'Unknown';
            }
            document.getElementById('interval-count').textContent = intervalCount;
        }
        
        function testReload() {
            log('🧪 Testing reload block...');
            try {
                window.location.reload();
                log('❌ Reload was NOT blocked!');
            } catch (e) {
                log('✅ Reload was blocked: ' + e.message);
            }
        }
        
        function testTimeout() {
            log('🧪 Testing auto-refresh timeout block...');
            setTimeout(() => {
                window.location.reload();
            }, 1000);
            log('⏳ Auto-refresh timeout set - should be blocked');
        }
        
        function manualRefresh() {
            log('🔄 Manual refresh requested by user');
            window.location.reload(true);
        }
        
        // Monitor for blocked reload attempts
        const originalReload = window.location.reload;
        if (originalReload.toString().includes('ErrorLoopFix')) {
            log('✅ Reload override detected - auto-refresh protection is active');
        }
        
        // Monitor for blocked timeouts
        const originalSetTimeout = window.setTimeout;
        if (originalSetTimeout.toString().includes('ErrorLoopFix')) {
            log('✅ Timeout override detected - auto-refresh timeout protection is active');
        }
        
        // Update counters every second
        setInterval(updateCounters, 1000);
        
        // Initial status
        log('🚫 Auto-refresh fix test started');
        log('⏱️ Monitoring page stability...');
        
        // Check if error loop fix is applied
        if (window.ERROR_LOOP_FIX_APPLIED) {
            log('✅ Error loop fix is applied');
            document.getElementById('status-text').textContent = '✅ Auto-refresh protection is active';
            document.getElementById('status-text').parentElement.className = 'status success';
        } else {
            log('❌ Error loop fix is NOT applied');
            document.getElementById('status-text').textContent = '❌ Auto-refresh protection is NOT active';
            document.getElementById('status-text').parentElement.className = 'status error';
        }
        
        // Listen for error loop fix completion
        window.addEventListener('errorLoopFixComplete', (event) => {
            log('🎉 Error loop fix completed: ' + event.detail.fixes.join(', '));
        });
        
        // Monitor for any unexpected page unloads
        window.addEventListener('beforeunload', (event) => {
            log('⚠️ Page unload detected - this should only happen for manual navigation');
        });
        
        // Test that we can stay on the page for at least 30 seconds
        setTimeout(() => {
            if (document.getElementById('uptime').textContent >= 30) {
                log('🎉 SUCCESS: Page has been stable for 30+ seconds!');
                document.getElementById('status-text').textContent = '🎉 Page is stable - auto-refresh successfully blocked!';
                document.getElementById('status-text').parentElement.className = 'status success';
            }
        }, 30000);
    </script>
</body>
</html>
