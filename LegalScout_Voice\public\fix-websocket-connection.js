/**
 * Fix for WebSocket Connection Issues
 * 
 * This script adds a fallback mechanism for WebSocket connections
 * to prevent errors when the WebSocket connection fails.
 */

(function() {
  console.log('[FixWebSocketConnection] Starting fix...');

  // Create a mock WebSocket class
  class MockWebSocket {
    constructor(url) {
      console.log('[MockWebSocket] Creating mock WebSocket for:', url);
      this.url = url;
      this.readyState = WebSocket.CONNECTING;
      
      // Simulate connection
      setTimeout(() => {
        this.readyState = WebSocket.OPEN;
        if (this.onopen) {
          this.onopen({ target: this });
        }
      }, 500);
    }
    
    send(data) {
      console.log('[MockWebSocket] Sending data:', data);
      // Simulate message response
      setTimeout(() => {
        if (this.onmessage) {
          this.onmessage({
            data: JSON.stringify({
              type: 'mock-response',
              data: { success: true, message: 'Mock response' }
            }),
            target: this
          });
        }
      }, 100);
    }
    
    close() {
      console.log('[MockWebSocket] Closing connection');
      this.readyState = WebSocket.CLOSED;
      if (this.onclose) {
        this.onclose({ target: this });
      }
    }
  }

  // Store the original WebSocket constructor
  const OriginalWebSocket = window.WebSocket;
  
  // Override the WebSocket constructor
  window.WebSocket = function(url, protocols) {
    console.log('[FixWebSocketConnection] Creating WebSocket for:', url);
    
    // Try to create a real WebSocket
    try {
      return new OriginalWebSocket(url, protocols);
    } catch (error) {
      console.warn('[FixWebSocketConnection] Error creating WebSocket:', error);
      console.log('[FixWebSocketConnection] Falling back to mock WebSocket');
      
      // Fall back to mock WebSocket
      return new MockWebSocket(url);
    }
  };
  
  // Copy static properties from original WebSocket
  window.WebSocket.CONNECTING = OriginalWebSocket.CONNECTING;
  window.WebSocket.OPEN = OriginalWebSocket.OPEN;
  window.WebSocket.CLOSING = OriginalWebSocket.CLOSING;
  window.WebSocket.CLOSED = OriginalWebSocket.CLOSED;
  
  console.log('[FixWebSocketConnection] Fix applied successfully');
})();
