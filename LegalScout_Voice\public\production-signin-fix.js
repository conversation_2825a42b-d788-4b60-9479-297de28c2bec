/**
 * Production Sign-In Fix
 * 
 * This script fixes critical issues that occur during sign-in in production:
 * 1. CORS issues with Vapi MCP server
 * 2. Missing Supabase API keys in requests
 * 3. MutationObserver errors
 * 4. Environment variable access issues
 */

console.log('[ProductionSignInFix] Starting production sign-in fixes...');

// 1. Fix Environment Variables Access
(function fixEnvironmentVariables() {
  console.log('[ProductionSignInFix] Fixing environment variables...');
  
  // Ensure critical environment variables are available
  if (typeof window !== 'undefined') {
    // Supabase configuration
    if (!window.VITE_SUPABASE_URL) {
      window.VITE_SUPABASE_URL = 'https://utopqxsvudgrtiwenlzl.supabase.co';
    }
    
    if (!window.VITE_SUPABASE_KEY && !window.VITE_SUPABASE_ANON_KEY) {
      window.VITE_SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzg5NDgwMDcsImV4cCI6MjA1NDUyNDAwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU';
      window.VITE_SUPABASE_ANON_KEY = window.VITE_SUPABASE_KEY;
    }
    
    // Vapi configuration
    if (!window.VITE_VAPI_PUBLIC_KEY) {
      window.VITE_VAPI_PUBLIC_KEY = '310f0d43-27c2-47a5-a76d-e55171d024f7';
    }
    
    if (!window.VITE_VAPI_SECRET_KEY) {
      window.VITE_VAPI_SECRET_KEY = '6734febc-fc65-4669-93b0-929b31ff6564';
    }
    
    console.log('[ProductionSignInFix] Environment variables configured');
  }
})();

// 2. Fix Supabase API Key Issues
(function fixSupabaseApiKey() {
  console.log('[ProductionSignInFix] Fixing Supabase API key issues...');
  
  // Override fetch to ensure Supabase requests always have the API key
  const originalFetch = window.fetch;
  
  window.fetch = function(url, options = {}) {
    // Check if this is a Supabase request
    if (typeof url === 'string' && url.includes('supabase.co')) {
      console.log('[ProductionSignInFix] Intercepting Supabase request:', url);
      
      // Ensure headers exist
      options.headers = options.headers || {};
      
      // Convert Headers object to plain object if needed
      if (options.headers instanceof Headers) {
        const headersObj = {};
        for (const [key, value] of options.headers.entries()) {
          headersObj[key] = value;
        }
        options.headers = headersObj;
      }
      
      // Get the API key from various sources
      const apiKey = options.headers.apikey || 
                    options.headers.Authorization?.replace('Bearer ', '') ||
                    window.VITE_SUPABASE_KEY ||
                    window.VITE_SUPABASE_ANON_KEY ||
                    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzg5NDgwMDcsImV4cCI6MjA1NDUyNDAwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU';
      
      // Ensure required headers are present
      options.headers.apikey = apiKey;
      options.headers.Authorization = options.headers.Authorization || `Bearer ${apiKey}`;
      options.headers['Content-Type'] = options.headers['Content-Type'] || 'application/json';
      options.headers.Accept = options.headers.Accept || 'application/json';
      options.headers.Prefer = options.headers.Prefer || 'return=representation';
      
      console.log('[ProductionSignInFix] Added Supabase headers:', {
        hasApiKey: !!options.headers.apikey,
        hasAuth: !!options.headers.Authorization,
        contentType: options.headers['Content-Type']
      });
    }
    
    return originalFetch.call(this, url, options);
  };
  
  console.log('[ProductionSignInFix] Supabase fetch interceptor installed');
})();

// 3. Fix CORS Issues with Vapi MCP
(function fixVapiMcpCors() {
  console.log('[ProductionSignInFix] Fixing Vapi MCP CORS issues...');
  
  // Override fetch for Vapi MCP requests to handle CORS
  const originalFetch = window.fetch;
  
  window.fetch = function(url, options = {}) {
    // Check if this is a Vapi MCP request
    if (typeof url === 'string' && url.includes('mcp.vapi.ai')) {
      console.log('[ProductionSignInFix] Intercepting Vapi MCP request:', url);
      
      // For CORS-blocked requests, fall back to direct API
      console.warn('[ProductionSignInFix] Vapi MCP request detected - will fall back to direct API');
      
      // Return a rejected promise to trigger fallback
      return Promise.reject(new Error('CORS blocked - falling back to direct API'));
    }
    
    return originalFetch.call(this, url, options);
  };
  
  console.log('[ProductionSignInFix] Vapi MCP CORS handler installed');
})();

// 4. Fix MutationObserver Errors
(function fixMutationObserver() {
  console.log('[ProductionSignInFix] Fixing MutationObserver errors...');
  
  // Override MutationObserver.observe to handle invalid nodes
  const OriginalMutationObserver = window.MutationObserver;
  
  window.MutationObserver = function(callback) {
    const observer = new OriginalMutationObserver(callback);
    
    const originalObserve = observer.observe;
    observer.observe = function(target, options) {
      try {
        // Check if target is a valid Node
        if (!target || typeof target.nodeType !== 'number') {
          console.warn('[ProductionSignInFix] Invalid MutationObserver target:', target);
          return;
        }
        
        return originalObserve.call(this, target, options);
      } catch (error) {
        console.warn('[ProductionSignInFix] MutationObserver error caught:', error.message);
        // Silently fail instead of throwing
      }
    };
    
    return observer;
  };
  
  // Copy static methods
  window.MutationObserver.prototype = OriginalMutationObserver.prototype;
  
  console.log('[ProductionSignInFix] MutationObserver error handler installed');
})();

// 5. Fix Import Meta Environment Access
(function fixImportMetaEnv() {
  console.log('[ProductionSignInFix] Fixing import.meta.env access...');
  
  // Ensure import.meta.env is available with fallback values
  if (typeof window !== 'undefined') {
    // Create a mock import.meta if it doesn't exist
    if (typeof window.import === 'undefined') {
      window.import = {};
    }
    
    if (typeof window.import.meta === 'undefined') {
      window.import.meta = {};
    }
    
    if (typeof window.import.meta.env === 'undefined') {
      window.import.meta.env = {};
    }
    
    // Set environment variables
    const env = window.import.meta.env;
    env.VITE_SUPABASE_URL = env.VITE_SUPABASE_URL || window.VITE_SUPABASE_URL || 'https://utopqxsvudgrtiwenlzl.supabase.co';
    env.VITE_SUPABASE_KEY = env.VITE_SUPABASE_KEY || window.VITE_SUPABASE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzg5NDgwMDcsImV4cCI6MjA1NDUyNDAwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU';
    env.VITE_SUPABASE_ANON_KEY = env.VITE_SUPABASE_ANON_KEY || env.VITE_SUPABASE_KEY;
    env.VITE_VAPI_PUBLIC_KEY = env.VITE_VAPI_PUBLIC_KEY || window.VITE_VAPI_PUBLIC_KEY || '310f0d43-27c2-47a5-a76d-e55171d024f7';
    env.VITE_VAPI_SECRET_KEY = env.VITE_VAPI_SECRET_KEY || window.VITE_VAPI_SECRET_KEY || '6734febc-fc65-4669-93b0-929b31ff6564';
    env.MODE = env.MODE || 'production';
    env.DEV = false;
    env.PROD = true;
    
    console.log('[ProductionSignInFix] import.meta.env configured:', {
      hasSupabaseUrl: !!env.VITE_SUPABASE_URL,
      hasSupabaseKey: !!env.VITE_SUPABASE_KEY,
      hasVapiKey: !!env.VITE_VAPI_PUBLIC_KEY,
      mode: env.MODE
    });
  }
})();

// 6. Initialize fixes on DOM ready
(function initializeFixes() {
  console.log('[ProductionSignInFix] Initializing fixes...');
  
  // Wait for DOM to be ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', function() {
      console.log('[ProductionSignInFix] DOM ready - fixes initialized');
    });
  } else {
    console.log('[ProductionSignInFix] DOM already ready - fixes initialized');
  }
  
  console.log('[ProductionSignInFix] ✅ All production sign-in fixes applied successfully');
})();
