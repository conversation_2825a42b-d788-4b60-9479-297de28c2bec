/**
 * Unified Banner Fix
 * 
 * This script provides a single, unified solution for banner functionality
 * that doesn't conflict with React or other scripts.
 */

console.log('[UnifiedBannerFix] Starting unified banner fix...');

// Simple banner state management
let bannerState = {
  isRemoving: false,
  lastRemovalTime: 0
};

// Function to handle banner removal
function handleBannerRemoval() {
  console.log('[UnifiedBannerFix] Banner removal initiated');
  
  bannerState.isRemoving = true;
  bannerState.lastRemovalTime = Date.now();
  
  // Store removal state
  localStorage.setItem('banner_removed', 'true');
  localStorage.setItem('banner_removal_time', bannerState.lastRemovalTime.toString());
  
  // Wait a moment for React to process the removal, then ensure upload interface is visible
  setTimeout(() => {
    showUploadInterface();
    bannerState.isRemoving = false;
  }, 500);
}

// Function to show upload interface
function showUploadInterface() {
  try {
    console.log('[UnifiedBannerFix] Ensuring upload interface is visible');
    
    // Find banner upload containers
    const containers = document.querySelectorAll('.logo-upload-container');
    
    containers.forEach(container => {
      const uploadDiv = container.querySelector('.logo-upload');
      const previewDiv = container.querySelector('.logo-preview');
      
      if (uploadDiv && previewDiv) {
        // Hide preview, show upload
        previewDiv.style.display = 'none';
        uploadDiv.style.display = 'block';
        
        // Ensure file input is ready
        const fileInput = uploadDiv.querySelector('input[type="file"]');
        if (fileInput) {
          fileInput.disabled = false;
          fileInput.value = '';
          console.log('[UnifiedBannerFix] Upload interface is now visible and ready');
        }
      }
    });
  } catch (error) {
    console.error('[UnifiedBannerFix] Error showing upload interface:', error);
  }
}

// Function to handle new file upload
function handleFileUpload() {
  console.log('[UnifiedBannerFix] New file uploaded, clearing removal state');
  
  // Clear removal state
  localStorage.removeItem('banner_removed');
  localStorage.removeItem('banner_removal_time');
  bannerState.lastRemovalTime = 0;
}

// Function to check if banner was recently removed
function wasBannerRecentlyRemoved() {
  const removed = localStorage.getItem('banner_removed');
  const removalTime = localStorage.getItem('banner_removal_time');
  
  if (removed === 'true' && removalTime) {
    const timeDiff = Date.now() - parseInt(removalTime);
    return timeDiff < 60000; // 1 minute
  }
  
  return false;
}

// Function to set up event listeners
function setupEventListeners() {
  // Listen for remove button clicks - detect by text content and context
  document.addEventListener('click', function(event) {
    const target = event.target;

    if (target && target.tagName === 'BUTTON') {
      const buttonText = (target.textContent || target.innerText || '').toLowerCase();
      const buttonClass = target.className || '';
      const buttonId = target.id || '';

      // Check if this looks like a remove button for banner/logo
      const isRemoveButton = (
        (buttonText.includes('remove') && (buttonText.includes('banner') || buttonText.includes('logo'))) ||
        buttonClass.includes('remove-logo') ||
        buttonClass.includes('remove-banner') ||
        buttonId.includes('remove-logo') ||
        buttonId.includes('remove-banner') ||
        (buttonText.includes('remove') && target.closest('.logo-upload-container'))
      );

      if (isRemoveButton) {
        console.log('[UnifiedBannerFix] Remove button detected:', {
          text: buttonText,
          class: buttonClass,
          id: buttonId
        });

        // Let the original handler run first, then handle our logic
        setTimeout(() => {
          handleBannerRemoval();
        }, 100);
      }
    }
  }, true);

  // Also listen for any button clicks in banner areas and check if banner disappears
  document.addEventListener('click', function(event) {
    const target = event.target;

    if (target && target.tagName === 'BUTTON') {
      // Check if this button is within a banner/logo container
      const bannerContainer = target.closest('.logo-upload-container, .logo-preview, .banner-container');

      if (bannerContainer) {
        console.log('[UnifiedBannerFix] Button clicked in banner area, monitoring for removal');

        // Check if banner disappears after this click
        setTimeout(() => {
          const logoImages = document.querySelectorAll('img[class*="logo"], img[alt*="Banner"], img[src*="logo"]');
          let bannerVisible = false;

          logoImages.forEach(img => {
            if (img.src && img.src !== '' && img.style.display !== 'none') {
              bannerVisible = true;
            }
          });

          if (!bannerVisible) {
            console.log('[UnifiedBannerFix] Banner disappeared after button click, handling removal');
            handleBannerRemoval();
          }
        }, 200);
      }
    }
  }, true);

  // Listen for file input changes
  document.addEventListener('change', function(event) {
    const target = event.target;
    
    if (target && target.type === 'file' && target.id === 'logo-upload') {
      if (target.files && target.files.length > 0) {
        handleFileUpload();
      }
    }
  });
  
  console.log('[UnifiedBannerFix] Event listeners set up');
}

// Function to monitor and fix interface state
function monitorInterfaceState() {
  let lastBannerState = null;

  setInterval(() => {
    // Check current banner state
    const logoImages = document.querySelectorAll('img[class*="logo"], img[alt*="Banner"], img[src*="logo"]');
    let currentBannerVisible = false;

    logoImages.forEach(img => {
      if (img.src && img.src !== '' && img.style.display !== 'none' && !img.src.includes('undefined')) {
        currentBannerVisible = true;
      }
    });

    // If banner state changed from visible to not visible, handle removal
    if (lastBannerState === true && currentBannerVisible === false) {
      console.log('[UnifiedBannerFix] Banner state changed from visible to hidden, handling removal');
      handleBannerRemoval();
    }

    lastBannerState = currentBannerVisible;

    // Also check if we need to fix interface state
    if (wasBannerRecentlyRemoved()) {
      const containers = document.querySelectorAll('.logo-upload-container');
      let needsFix = false;

      containers.forEach(container => {
        const uploadDiv = container.querySelector('.logo-upload');
        const previewDiv = container.querySelector('.logo-preview');

        if (uploadDiv && previewDiv) {
          // If preview is visible but banner was removed, fix it
          if (previewDiv.style.display !== 'none') {
            const img = previewDiv.querySelector('img');
            if (!img || !img.src || img.src === '' || img.src.includes('undefined')) {
              needsFix = true;
            }
          }
        }
      });

      if (needsFix) {
        console.log('[UnifiedBannerFix] Interface state needs fixing');
        showUploadInterface();
      }
    }
  }, 1000); // Check more frequently

  console.log('[UnifiedBannerFix] Interface state monitoring started');
}

// Function to handle page load
function handlePageLoad() {
  // Check if banner was recently removed on page load
  if (wasBannerRecentlyRemoved()) {
    console.log('[UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible');
    setTimeout(() => {
      showUploadInterface();
    }, 1000);
  }
}

// Function to disable conflicting scripts
function disableConflictingScripts() {
  // Disable other banner-related global functions to prevent conflicts
  const functionsToDisable = [
    'restoreBannerUploadFunctionality',
    'patchBannerRemoveHandlers',
    'monitorLogoRestoration',
    'ensureUploadInterfaceVisible'
  ];
  
  functionsToDisable.forEach(funcName => {
    if (window[funcName]) {
      console.log('[UnifiedBannerFix] Disabling conflicting function:', funcName);
      window[funcName] = function() {
        console.log('[UnifiedBannerFix] Conflicting function disabled:', funcName);
      };
    }
  });
  
  // Override safeBannerManager if it exists
  if (window.safeBannerManager) {
    const originalMarkAsRemoved = window.safeBannerManager.markAsRemoved;
    window.safeBannerManager.markAsRemoved = function() {
      console.log('[UnifiedBannerFix] Using unified banner removal instead of safeBannerManager');
      handleBannerRemoval();
    };
  }
}

// Function to create a simple, working banner system
function createSimpleBannerSystem() {
  // Override any complex banner management with simple logic
  window.simpleBannerManager = {
    remove: function() {
      console.log('[UnifiedBannerFix] Simple banner removal');
      handleBannerRemoval();
    },
    
    upload: function() {
      console.log('[UnifiedBannerFix] Simple banner upload');
      handleFileUpload();
    },
    
    showUpload: function() {
      showUploadInterface();
    }
  };
  
  console.log('[UnifiedBannerFix] Simple banner system created');
}

// Main initialization function
function initialize() {
  try {
    disableConflictingScripts();
    createSimpleBannerSystem();
    setupEventListeners();
    monitorInterfaceState();
    handlePageLoad();
    
    console.log('[UnifiedBannerFix] Unified banner fix initialized successfully');
  } catch (error) {
    console.error('[UnifiedBannerFix] Error during initialization:', error);
  }
}

// Initialize immediately
initialize();

// Also initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initialize);
} else {
  setTimeout(initialize, 100);
}

// Reinitialize after other scripts have loaded
setTimeout(initialize, 2000);

console.log('[UnifiedBannerFix] Unified banner fix script loaded');
