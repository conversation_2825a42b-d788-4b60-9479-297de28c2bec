/**
 * Global Attorney State Coordinator
 * 
 * Provides unified attorney state management across dashboard, subdomain, and preview contexts.
 * Ensures data consistency, handles conflicts, and provides real-time synchronization.
 */

(function() {
  'use strict';

  console.log('[GlobalAttorneyCoordinator] Initializing...');

  // Prevent multiple instances
  if (window.GlobalAttorneyCoordinator) {
    console.warn('[GlobalAttorneyCoordinator] Already initialized, using existing instance');
    return;
  }

  class GlobalAttorneyCoordinator {
    constructor() {
      this.state = {
        attorney: null,
        isInitialized: false,
        lastUpdated: null,
        activeContexts: new Set(),
        pendingUpdates: new Map(),
        initializationLock: false,
        initializationQueue: []
      };

      this.listeners = {
        stateChange: new Set(),
        contextJoin: new Set(),
        contextLeave: new Set(),
        error: new Set(),
        beforeUpdate: new Set(),
        afterUpdate: new Set()
      };

      this.contexts = new Map();
      this.heartbeatInterval = null;
      this.syncInterval = null;

      // Storage configuration
      this.storage = {
        keys: {
          attorney: 'global_attorney_state',
          metadata: 'global_attorney_metadata',
          contexts: 'global_attorney_contexts'
        }
      };

      // Initialize heartbeat and cleanup
      this.startHeartbeat();
      this.setupStorageSync();
      this.setupUnloadHandler();

      console.log('[GlobalAttorneyCoordinator] Initialized successfully');
    }

    /**
     * Register a context (dashboard, subdomain, preview)
     */
    async registerContext(contextId, contextType, capabilities = {}) {
      console.log(`[GlobalAttorneyCoordinator] Registering context: ${contextId} (${contextType})`);

      const context = {
        id: contextId,
        type: contextType,
        capabilities,
        registeredAt: Date.now(),
        lastSeen: Date.now()
      };

      this.contexts.set(contextId, context);
      this.state.activeContexts.add(contextId);

      // Notify listeners
      this.emit('contextJoin', { context, coordinator: this });

      // If this is the first context, initialize
      if (this.contexts.size === 1 && !this.state.isInitialized) {
        await this.initialize(contextId);
      }

      return context;
    }

    /**
     * Initialize attorney state with conflict resolution
     */
    async initialize(initiatingContextId) {
      // Handle concurrent initialization
      if (this.state.initializationLock) {
        console.log('[GlobalAttorneyCoordinator] Initialization in progress, queuing...');
        return new Promise((resolve) => {
          this.state.initializationQueue.push(resolve);
        });
      }

      this.state.initializationLock = true;

      try {
        console.log(`[GlobalAttorneyCoordinator] Initializing from context: ${initiatingContextId}`);

        // Step 1: Try to load from memory (if already initialized)
        if (this.state.attorney && this.state.isInitialized) {
          console.log('[GlobalAttorneyCoordinator] Already initialized, returning existing state');
          return this.state.attorney;
        }

        // Step 2: Try to load from localStorage
        let attorney = this.loadFromStorage();
        if (attorney && this.validateAttorney(attorney)) {
          console.log('[GlobalAttorneyCoordinator] Loaded attorney from storage:', attorney.id);
          this.setState({ attorney, isInitialized: true });
          return attorney;
        }

        // Step 3: Determine loading strategy based on context
        const context = this.contexts.get(initiatingContextId);
        if (!context) {
          throw new Error(`Context ${initiatingContextId} not found`);
        }

        // Step 4: Load attorney based on context type
        attorney = await this.loadAttorneyByContext(context);
        if (attorney && this.validateAttorney(attorney)) {
          console.log('[GlobalAttorneyCoordinator] Loaded attorney from Supabase:', attorney.id);
          this.setState({ attorney, isInitialized: true });
          this.saveToStorage(attorney);
          return attorney;
        }

        throw new Error('No valid attorney found');

      } catch (error) {
        console.error('[GlobalAttorneyCoordinator] Initialization error:', error);
        this.emit('error', { error, context: 'initialization' });
        throw error;
      } finally {
        this.state.initializationLock = false;
        
        // Process queued initializations
        const queuedResolvers = this.state.initializationQueue.splice(0);
        queuedResolvers.forEach(resolve => resolve(this.state.attorney));
      }
    }

    /**
     * Load attorney based on context type
     */
    async loadAttorneyByContext(context) {
      if (!window.supabase) {
        throw new Error('Supabase not available');
      }

      switch (context.type) {
        case 'dashboard':
          // Dashboard: Load by authenticated user
          return await this.loadAttorneyByAuth();
          
        case 'subdomain':
          // Subdomain: Load by subdomain
          const subdomain = this.getCurrentSubdomain();
          return await this.loadAttorneyBySubdomain(subdomain);
          
        case 'preview':
          // Preview: Load by URL parameters or fallback
          const urlParams = new URLSearchParams(window.location.search);
          const previewSubdomain = urlParams.get('subdomain');
          if (previewSubdomain) {
            return await this.loadAttorneyBySubdomain(previewSubdomain);
          }
          return await this.loadAttorneyByAuth();
          
        default:
          throw new Error(`Unknown context type: ${context.type}`);
      }
    }

    /**
     * Load attorney by authenticated user (dashboard context)
     */
    async loadAttorneyByAuth() {
      // Try to get user from auth context
      const user = window.user || window.currentUser;
      if (user?.email) {
        return await this.loadAttorneyByEmail(user.email);
      }

      // Try to get user from Supabase auth
      if (window.supabase) {
        try {
          const { data: { user: authUser } } = await window.supabase.auth.getUser();
          if (authUser?.email) {
            return await this.loadAttorneyByEmail(authUser.email);
          }
        } catch (error) {
          console.error('Error getting authenticated user:', error);
        }
      }

      // No authenticated user found
      throw new Error('No authenticated user found');
    }

    /**
     * Load attorney by email
     */
    async loadAttorneyByEmail(email) {
      const { data, error } = await window.supabase
        .from('attorneys')
        .select('*')
        .eq('email', email)
        .order('updated_at', { ascending: false })
        .limit(1)
        .single();

      if (error) throw error;
      return data;
    }

    /**
     * Load attorney by subdomain
     */
    async loadAttorneyBySubdomain(subdomain) {
      if (!subdomain || subdomain === 'default' || subdomain === 'www') {
        throw new Error('Invalid subdomain');
      }

      const { data, error } = await window.supabase
        .from('attorneys')
        .select('*')
        .eq('subdomain', subdomain)
        .single();

      if (error) throw error;
      return data;
    }

    /**
     * Get current subdomain
     */
    getCurrentSubdomain() {
      const host = window.location.hostname;
      const parts = host.split('.');
      
      if (parts.length > 2 && parts[0] !== 'www') {
        return parts[0];
      }
      
      return 'default';
    }

    /**
     * Update attorney state with conflict resolution
     */
    async updateAttorney(updates, contextId) {
      console.log(`[GlobalAttorneyCoordinator] Updating attorney from context: ${contextId}`);

      try {
        // Emit before update event
        this.emit('beforeUpdate', { updates, contextId, currentAttorney: this.state.attorney });

        // Validate updates
        if (!this.state.attorney) {
          throw new Error('No attorney to update');
        }

        // Create updated attorney
        const updatedAttorney = {
          ...this.state.attorney,
          ...updates,
          updated_at: new Date().toISOString()
        };

        // Validate the updated attorney
        if (!this.validateAttorney(updatedAttorney)) {
          throw new Error('Updated attorney data is invalid');
        }

        // Update state
        this.setState({ attorney: updatedAttorney });

        // Save to storage
        this.saveToStorage(updatedAttorney);

        // Emit after update event
        this.emit('afterUpdate', { attorney: updatedAttorney, contextId });

        console.log('[GlobalAttorneyCoordinator] Attorney updated successfully:', updatedAttorney.id);
        return updatedAttorney;

      } catch (error) {
        console.error('[GlobalAttorneyCoordinator] Update error:', error);
        this.emit('error', { error, context: 'update', contextId });
        throw error;
      }
    }

    /**
     * Set state and notify listeners
     */
    setState(newState) {
      const oldState = { ...this.state };
      Object.assign(this.state, newState, { lastUpdated: Date.now() });
      
      // Notify state change listeners
      this.emit('stateChange', {
        oldState,
        newState: this.state,
        attorney: this.state.attorney
      });
    }

    /**
     * Validate attorney data
     */
    validateAttorney(attorney) {
      return attorney && 
             typeof attorney === 'object' && 
             attorney.id && 
             typeof attorney.id === 'string' && 
             attorney.id.length > 0 &&
             !Array.isArray(attorney);
    }

    /**
     * Load attorney from storage with validation
     */
    loadFromStorage() {
      try {
        const stored = localStorage.getItem(this.storage.keys.attorney);
        if (!stored) return null;

        const attorney = JSON.parse(stored);
        return this.validateAttorney(attorney) ? attorney : null;
      } catch (error) {
        console.error('[GlobalAttorneyCoordinator] Storage load error:', error);
        return null;
      }
    }

    /**
     * Save attorney to storage
     */
    saveToStorage(attorney) {
      try {
        if (!this.validateAttorney(attorney)) {
          throw new Error('Invalid attorney data');
        }

        localStorage.setItem(this.storage.keys.attorney, JSON.stringify(attorney));
        
        // Also save to legacy keys for compatibility
        localStorage.setItem('attorney', JSON.stringify(attorney));
        localStorage.setItem('attorney_profile', JSON.stringify(attorney));
        
        console.log('[GlobalAttorneyCoordinator] Saved attorney to storage:', attorney.id);
      } catch (error) {
        console.error('[GlobalAttorneyCoordinator] Storage save error:', error);
      }
    }

    /**
     * Event system
     */
    on(event, callback) {
      if (!this.listeners[event]) {
        this.listeners[event] = new Set();
      }
      this.listeners[event].add(callback);
      
      return () => this.listeners[event].delete(callback); // Return unsubscribe function
    }

    emit(event, data) {
      if (this.listeners[event]) {
        this.listeners[event].forEach(callback => {
          try {
            callback(data);
          } catch (error) {
            console.error(`[GlobalAttorneyCoordinator] Listener error for ${event}:`, error);
          }
        });
      }
    }

    /**
     * Get current attorney
     */
    getAttorney() {
      return this.state.attorney;
    }

    /**
     * Check if initialized
     */
    isInitialized() {
      return this.state.isInitialized;
    }

    /**
     * Heartbeat system for context management
     */
    startHeartbeat() {
      this.heartbeatInterval = setInterval(() => {
        const now = Date.now();
        const timeout = 30000; // 30 seconds

        // Clean up stale contexts
        for (const [contextId, context] of this.contexts.entries()) {
          if (now - context.lastSeen > timeout) {
            console.log(`[GlobalAttorneyCoordinator] Context ${contextId} timed out`);
            this.unregisterContext(contextId);
          }
        }
      }, 10000); // Check every 10 seconds
    }

    /**
     * Update context heartbeat
     */
    updateHeartbeat(contextId) {
      const context = this.contexts.get(contextId);
      if (context) {
        context.lastSeen = Date.now();
      }
    }

    /**
     * Unregister context
     */
    unregisterContext(contextId) {
      const context = this.contexts.get(contextId);
      if (context) {
        this.contexts.delete(contextId);
        this.state.activeContexts.delete(contextId);
        this.emit('contextLeave', { context, coordinator: this });
        console.log(`[GlobalAttorneyCoordinator] Unregistered context: ${contextId}`);
      }
    }

    /**
     * Setup storage synchronization
     */
    setupStorageSync() {
      // Listen for storage changes from other tabs
      window.addEventListener('storage', (event) => {
        if (event.key === this.storage.keys.attorney && event.newValue) {
          try {
            const attorney = JSON.parse(event.newValue);
            if (this.validateAttorney(attorney) && attorney.id !== this.state.attorney?.id) {
              console.log('[GlobalAttorneyCoordinator] Syncing attorney from storage change');
              this.setState({ attorney });
            }
          } catch (error) {
            console.error('[GlobalAttorneyCoordinator] Storage sync error:', error);
          }
        }
      });
    }

    /**
     * Setup unload handler
     */
    setupUnloadHandler() {
      window.addEventListener('beforeunload', () => {
        // Clean up intervals
        if (this.heartbeatInterval) {
          clearInterval(this.heartbeatInterval);
        }
        if (this.syncInterval) {
          clearInterval(this.syncInterval);
        }
      });
    }

    /**
     * Destroy coordinator
     */
    destroy() {
      // Clear intervals
      if (this.heartbeatInterval) {
        clearInterval(this.heartbeatInterval);
      }
      if (this.syncInterval) {
        clearInterval(this.syncInterval);
      }

      // Clear listeners
      Object.keys(this.listeners).forEach(event => {
        this.listeners[event].clear();
      });

      // Clear contexts
      this.contexts.clear();
      this.state.activeContexts.clear();

      console.log('[GlobalAttorneyCoordinator] Destroyed');
    }
  }

  // Create global instance
  window.GlobalAttorneyCoordinator = new GlobalAttorneyCoordinator();

  console.log('[GlobalAttorneyCoordinator] Global instance created');

})();
