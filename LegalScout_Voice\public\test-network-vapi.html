<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vapi Network Diagnostic</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-result {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
            font-size: 12px;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-online { background-color: #28a745; }
        .status-offline { background-color: #dc3545; }
        .status-unknown { background-color: #6c757d; }
    </style>
</head>
<body>
    <h1>🌐 Vapi Network Diagnostic Tool</h1>
    
    <div class="test-container">
        <h2>Quick Actions</h2>
        <button onclick="runAllTests()">🚀 Run All Tests</button>
        <button onclick="testVapiCDNs()">📡 Test CDN Access</button>
        <button onclick="testVapiAPI()">🔌 Test API Access</button>
        <button onclick="testDNSResolution()">🔍 Test DNS Resolution</button>
        <button onclick="clearResults()">🧹 Clear Results</button>
    </div>

    <div class="grid">
        <div class="test-container">
            <h2>CDN Status</h2>
            <div id="cdn-status"></div>
        </div>
        
        <div class="test-container">
            <h2>API Status</h2>
            <div id="api-status"></div>
        </div>
    </div>

    <div class="test-container">
        <h2>Test Results</h2>
        <div id="test-results"></div>
    </div>

    <div class="test-container">
        <h2>Network Logs</h2>
        <div id="network-logs"></div>
    </div>

    <script>
        const resultsDiv = document.getElementById('test-results');
        const logsDiv = document.getElementById('network-logs');
        const cdnStatusDiv = document.getElementById('cdn-status');
        const apiStatusDiv = document.getElementById('api-status');

        function addResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            resultsDiv.appendChild(div);
        }

        function addLog(message) {
            const div = document.createElement('div');
            div.innerHTML = `<pre>${new Date().toISOString()}: ${message}</pre>`;
            logsDiv.appendChild(div);
            console.log(message);
        }

        function updateStatus(containerId, service, status, message) {
            const container = document.getElementById(containerId);
            const statusClass = status === 'online' ? 'status-online' : 
                               status === 'offline' ? 'status-offline' : 'status-unknown';
            
            const statusHtml = `
                <div style="margin: 10px 0;">
                    <span class="status-indicator ${statusClass}"></span>
                    <strong>${service}</strong>: ${message}
                </div>
            `;
            
            container.innerHTML += statusHtml;
        }

        function clearResults() {
            resultsDiv.innerHTML = '';
            logsDiv.innerHTML = '';
            cdnStatusDiv.innerHTML = '';
            apiStatusDiv.innerHTML = '';
        }

        async function testVapiCDNs() {
            addResult('🧪 Testing Vapi CDN accessibility...', 'info');
            
            const cdnSources = [
                { name: 'UNPKG CDN (Official)', url: 'https://unpkg.com/@vapi-ai/web@latest/dist/index.umd.js' },
                { name: 'JSDelivr CDN (Official)', url: 'https://cdn.jsdelivr.net/npm/@vapi-ai/web@latest/dist/index.umd.js' },
                { name: 'Vapi CDN (Non-existent)', url: 'https://cdn.vapi.ai/web-sdk@2.2.2/dist/web-sdk.js' }
            ];

            for (const cdn of cdnSources) {
                try {
                    addLog(`Testing ${cdn.name}: ${cdn.url}`);
                    
                    const startTime = Date.now();
                    const response = await fetch(cdn.url, { 
                        method: 'HEAD',
                        mode: 'no-cors' // Avoid CORS issues for testing
                    });
                    const loadTime = Date.now() - startTime;
                    
                    addLog(`${cdn.name} response: ${response.type} (${loadTime}ms)`);
                    updateStatus('cdn-status', cdn.name, 'online', `Accessible (${loadTime}ms)`);
                    addResult(`✅ ${cdn.name}: Accessible`, 'success');
                    
                } catch (error) {
                    addLog(`${cdn.name} error: ${error.message}`);
                    updateStatus('cdn-status', cdn.name, 'offline', `Error: ${error.message}`);
                    addResult(`❌ ${cdn.name}: ${error.message}`, 'error');
                }
            }
        }

        async function testVapiAPI() {
            addResult('🧪 Testing Vapi API endpoints...', 'info');
            
            const apiEndpoints = [
                { name: 'Vapi API', url: 'https://api.vapi.ai' },
                { name: 'Vapi MCP', url: 'https://mcp.vapi.ai' },
                { name: 'Vapi Dashboard', url: 'https://dashboard.vapi.ai' }
            ];

            for (const api of apiEndpoints) {
                try {
                    addLog(`Testing ${api.name}: ${api.url}`);
                    
                    const startTime = Date.now();
                    const response = await fetch(api.url, { 
                        method: 'HEAD',
                        mode: 'no-cors'
                    });
                    const loadTime = Date.now() - startTime;
                    
                    addLog(`${api.name} response: ${response.type} (${loadTime}ms)`);
                    updateStatus('api-status', api.name, 'online', `Accessible (${loadTime}ms)`);
                    addResult(`✅ ${api.name}: Accessible`, 'success');
                    
                } catch (error) {
                    addLog(`${api.name} error: ${error.message}`);
                    updateStatus('api-status', api.name, 'offline', `Error: ${error.message}`);
                    addResult(`❌ ${api.name}: ${error.message}`, 'error');
                }
            }
        }

        async function testDNSResolution() {
            addResult('🧪 Testing DNS resolution...', 'info');
            
            const domains = [
                'cdn.vapi.ai',
                'api.vapi.ai', 
                'mcp.vapi.ai',
                'dashboard.vapi.ai',
                'unpkg.com',
                'cdn.jsdelivr.net'
            ];

            for (const domain of domains) {
                try {
                    addLog(`Testing DNS for ${domain}`);
                    
                    // Use a simple image request to test DNS resolution
                    const img = new Image();
                    const testPromise = new Promise((resolve, reject) => {
                        img.onload = () => resolve('resolved');
                        img.onerror = () => resolve('resolved'); // Domain exists even if image fails
                        setTimeout(() => reject(new Error('DNS timeout')), 5000);
                    });
                    
                    img.src = `https://${domain}/favicon.ico?t=${Date.now()}`;
                    
                    await testPromise;
                    addResult(`✅ DNS for ${domain}: Resolved`, 'success');
                    addLog(`DNS for ${domain}: Resolved successfully`);
                    
                } catch (error) {
                    addResult(`❌ DNS for ${domain}: ${error.message}`, 'error');
                    addLog(`DNS for ${domain}: ${error.message}`);
                }
            }
        }

        async function testVapiSDKLoading() {
            addResult('🧪 Testing actual Vapi SDK loading...', 'info');
            
            const cdnSources = [
                'https://unpkg.com/@vapi-ai/web@latest/dist/index.umd.js',
                'https://cdn.jsdelivr.net/npm/@vapi-ai/web@latest/dist/index.umd.js'
            ];

            for (let i = 0; i < cdnSources.length; i++) {
                const url = cdnSources[i];
                
                try {
                    addLog(`Attempting to load Vapi SDK from: ${url}`);
                    
                    const script = document.createElement('script');
                    script.src = url;
                    
                    const loadPromise = new Promise((resolve, reject) => {
                        script.onload = () => {
                            if (window.Vapi) {
                                resolve('loaded');
                            } else {
                                reject(new Error('Script loaded but Vapi not available'));
                            }
                        };
                        script.onerror = () => reject(new Error('Script failed to load'));
                        setTimeout(() => reject(new Error('Load timeout')), 10000);
                    });
                    
                    document.head.appendChild(script);
                    await loadPromise;
                    
                    addResult(`✅ Vapi SDK loaded successfully from: ${url}`, 'success');
                    addLog(`Vapi SDK loaded, type: ${typeof window.Vapi}`);
                    
                    // Test creating instance
                    try {
                        const vapi = new window.Vapi('test-key');
                        addResult(`✅ Vapi instance created successfully`, 'success');
                        addLog(`Vapi instance methods: ${Object.getOwnPropertyNames(Object.getPrototypeOf(vapi)).slice(0, 5).join(', ')}...`);
                    } catch (instanceError) {
                        addResult(`⚠️ Vapi loaded but instance creation failed: ${instanceError.message}`, 'warning');
                    }
                    
                    return; // Success, exit loop
                    
                } catch (error) {
                    addResult(`❌ Failed to load from ${url}: ${error.message}`, 'error');
                    addLog(`Load failed: ${error.message}`);
                    
                    // Remove failed script
                    if (script.parentNode) {
                        script.parentNode.removeChild(script);
                    }
                }
            }
            
            addResult(`❌ Failed to load Vapi SDK from all sources`, 'error');
        }

        async function runAllTests() {
            clearResults();
            addResult('🚀 Starting comprehensive Vapi network diagnostic...', 'info');
            
            try {
                await testDNSResolution();
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                await testVapiCDNs();
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                await testVapiAPI();
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                await testVapiSDKLoading();
                
                addResult('🏁 All tests completed', 'info');
                
            } catch (error) {
                addResult(`❌ Test suite error: ${error.message}`, 'error');
            }
        }

        // Auto-run basic connectivity test on page load
        window.addEventListener('load', () => {
            addResult('🌐 Vapi Network Diagnostic Tool Loaded', 'info');
            addLog('Diagnostic tool initialized');
            
            // Show current network info
            addLog(`User Agent: ${navigator.userAgent}`);
            addLog(`Online: ${navigator.onLine}`);
            addLog(`Connection: ${navigator.connection?.effectiveType || 'unknown'}`);
        });
    </script>
</body>
</html>
