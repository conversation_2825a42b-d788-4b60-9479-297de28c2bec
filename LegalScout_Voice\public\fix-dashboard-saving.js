/**
 * Fix Dashboard Saving Issues
 * 
 * This script fixes issues with saving dashboard changes to Supabase
 * and ensures proper data persistence.
 */

console.log('[FixDashboardSaving] Starting fix...');

// Function to ensure Supabase connection and save attorney data
async function saveAttorneyToSupabase(attorney) {
  try {
    if (!window.supabase) {
      console.warn('[FixDashboardSaving] Supabase not available');
      return { success: false, error: 'Supabase not available' };
    }
    
    if (!attorney || !attorney.id) {
      console.warn('[FixDashboardSaving] Invalid attorney data');
      return { success: false, error: 'Invalid attorney data' };
    }
    
    console.log('[FixDashboardSaving] Saving attorney to Supabase:', attorney.id);
    
    // Prepare attorney data for Supabase
    const attorneyData = {
      id: attorney.id,
      subdomain: attorney.subdomain,
      firm_name: attorney.firm_name,
      name: attorney.name,
      email: attorney.email,
      phone: attorney.phone,
      is_active: attorney.is_active !== false, // Default to true
      welcome_message: attorney.welcome_message,
      information_gathering_prompt: attorney.information_gathering_prompt,
      vapi_instructions: attorney.vapi_instructions,
      vapi_assistant_id: attorney.vapi_assistant_id,
      voice_provider: attorney.voice_provider,
      voice_id: attorney.voice_id,
      ai_model: attorney.ai_model,
      updated_at: new Date().toISOString()
    };
    
    // Remove undefined values
    Object.keys(attorneyData).forEach(key => {
      if (attorneyData[key] === undefined) {
        delete attorneyData[key];
      }
    });
    
    // Use upsert to insert or update
    const { data, error } = await window.supabase
      .from('attorneys')
      .upsert(attorneyData, {
        onConflict: 'id',
        returning: 'minimal'
      });
    
    if (error) {
      console.error('[FixDashboardSaving] Error saving to Supabase:', error);
      return { success: false, error: error.message };
    }
    
    console.log('[FixDashboardSaving] Successfully saved to Supabase');
    return { success: true, data };
    
  } catch (error) {
    console.error('[FixDashboardSaving] Exception saving to Supabase:', error);
    return { success: false, error: error.message };
  }
}

// Function to enhance the standalone attorney manager with proper saving
function enhanceStandaloneAttorneyManager() {
  if (!window.standaloneAttorneyManager) {
    return;
  }
  
  const manager = window.standaloneAttorneyManager;
  
  // Override the updateAttorney method to ensure Supabase saving
  const originalUpdateAttorney = manager.updateAttorney;
  manager.updateAttorney = async function(updates) {
    console.log('[FixDashboardSaving] Enhanced updateAttorney called with:', updates);
    
    try {
      // Call the original method first
      const result = await originalUpdateAttorney.call(this, updates);
      
      // Then save to Supabase
      if (this.attorney) {
        const saveResult = await saveAttorneyToSupabase(this.attorney);
        if (!saveResult.success) {
          console.warn('[FixDashboardSaving] Failed to save to Supabase:', saveResult.error);
        }
      }
      
      return result;
    } catch (error) {
      console.error('[FixDashboardSaving] Error in enhanced updateAttorney:', error);
      throw error;
    }
  };
  
  // Override the saveToLocalStorage method to also save to Supabase
  const originalSaveToLocalStorage = manager.saveToLocalStorage;
  manager.saveToLocalStorage = function(attorney) {
    // Call the original method first
    originalSaveToLocalStorage.call(this, attorney);
    
    // Then save to Supabase asynchronously
    const attorneyToSave = attorney || this.attorney;
    if (attorneyToSave) {
      saveAttorneyToSupabase(attorneyToSave).catch(error => {
        console.warn('[FixDashboardSaving] Background save to Supabase failed:', error);
      });
    }
  };
  
  // Add a method to manually save to Supabase
  manager.saveToSupabase = async function(attorney) {
    const attorneyToSave = attorney || this.attorney;
    return await saveAttorneyToSupabase(attorneyToSave);
  };
  
  console.log('[FixDashboardSaving] Enhanced standalone attorney manager with Supabase saving');
}

// Function to fix form submission handlers
function fixFormSubmissionHandlers() {
  // Wait for the React app to load and then enhance form handlers
  setTimeout(() => {
    // Look for form elements and enhance their submission
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
      // Add event listener for form submission
      form.addEventListener('submit', async (event) => {
        console.log('[FixDashboardSaving] Form submission detected');
        
        // If we have a standalone attorney manager, trigger a save
        if (window.standaloneAttorneyManager && window.standaloneAttorneyManager.attorney) {
          try {
            const saveResult = await saveAttorneyToSupabase(window.standaloneAttorneyManager.attorney);
            if (saveResult.success) {
              console.log('[FixDashboardSaving] Attorney data saved on form submission');
            }
          } catch (error) {
            console.warn('[FixDashboardSaving] Error saving on form submission:', error);
          }
        }
      });
    });
    
    // Also look for input changes and save periodically
    const inputs = document.querySelectorAll('input, textarea, select');
    inputs.forEach(input => {
      let saveTimeout;
      
      input.addEventListener('change', () => {
        // Clear existing timeout
        if (saveTimeout) {
          clearTimeout(saveTimeout);
        }
        
        // Set a new timeout to save after 2 seconds of no changes
        saveTimeout = setTimeout(async () => {
          if (window.standaloneAttorneyManager && window.standaloneAttorneyManager.attorney) {
            try {
              const saveResult = await saveAttorneyToSupabase(window.standaloneAttorneyManager.attorney);
              if (saveResult.success) {
                console.log('[FixDashboardSaving] Attorney data auto-saved after input change');
              }
            } catch (error) {
              console.warn('[FixDashboardSaving] Error auto-saving:', error);
            }
          }
        }, 2000);
      });
    });
    
    console.log('[FixDashboardSaving] Enhanced form submission handlers');
  }, 2000); // Wait 2 seconds for React to load
}

// Function to apply the fix
function applyFix() {
  try {
    enhanceStandaloneAttorneyManager();
    fixFormSubmissionHandlers();
    
    console.log('[FixDashboardSaving] Fix applied successfully');
    
  } catch (error) {
    console.error('[FixDashboardSaving] Error applying fix:', error);
  }
}

// Apply the fix immediately
applyFix();

// Also apply the fix when the DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', applyFix);
} else {
  // DOM is already ready, apply fix after a short delay
  setTimeout(applyFix, 100);
}

// Apply the fix when the standalone attorney manager becomes available
let checkCount = 0;
const maxChecks = 50; // Check for up to 5 seconds

function checkForStandaloneAttorneyManager() {
  checkCount++;
  
  if (window.standaloneAttorneyManager) {
    console.log('[FixDashboardSaving] Standalone attorney manager found, applying fix');
    enhanceStandaloneAttorneyManager();
    return;
  }
  
  if (checkCount < maxChecks) {
    setTimeout(checkForStandaloneAttorneyManager, 100);
  } else {
    console.log('[FixDashboardSaving] Standalone attorney manager not found after waiting');
  }
}

// Start checking for the standalone attorney manager
checkForStandaloneAttorneyManager();

console.log('[FixDashboardSaving] Fix script loaded');
