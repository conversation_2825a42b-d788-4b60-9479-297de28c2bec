/**
 * Manual fix for <PERSON>'s assistant conflict
 * This will create a new <NAME_EMAIL> and update the record
 */

window.fixDamonAssistantConflict = async function() {
  console.log('🔧 Fixing <PERSON>\'s assistant conflict...\n');

  try {
    // Import required modules
    const { supabase } = await import('/src/lib/supabase.js');
    const { createVapiAssistant } = await import('/src/services/syncHelpers.js');

    // Get <PERSON>'s attorney record
    const { data: attorney, error } = await supabase
      .from('attorneys')
      .select('*')
      .eq('email', '<EMAIL>')
      .single();

    if (error) {
      console.error('❌ Error fetching attorney:', error);
      return;
    }

    console.log('👤 Current attorney data:', {
      email: attorney.email,
      firmName: attorney.firm_name,
      subdomain: attorney.subdomain,
      currentAssistantId: attorney.vapi_assistant_id,
      instructions: attorney.vapi_instructions,
      welcomeMessage: attorney.welcome_message,
      voiceProvider: attorney.voice_provider,
      voiceId: attorney.voice_id
    });

    // Check who else is using this assistant ID
    const { data: conflictingAttorneys, error: conflictError } = await supabase
      .from('attorneys')
      .select('id, email, firm_name, subdomain')
      .eq('vapi_assistant_id', attorney.vapi_assistant_id)
      .neq('id', attorney.id);

    if (conflictError) {
      console.error('❌ Error checking conflicts:', conflictError);
      return;
    }

    if (conflictingAttorneys && conflictingAttorneys.length > 0) {
      console.log('⚠️ Assistant ID is shared with:', conflictingAttorneys);
    } else {
      console.log('✅ No conflicts found - assistant ID is unique');
      return { success: true, message: 'No conflicts to resolve' };
    }

    console.log('\n🔧 Creating new <NAME_EMAIL>...');

    // Create new assistant with Damon's configuration
    const newAssistant = await createVapiAssistant({
      name: attorney.firm_name || 'LegalScout Assistant',
      firstMessage: attorney.welcome_message || 'Hello! How can I help you today?',
      instructions: attorney.vapi_instructions || 'You are a legal assistant helping clients with their legal needs.',
      voice: {
        provider: attorney.voice_provider || 'openai',
        voiceId: attorney.voice_id || 'alloy'
      }
    });

    console.log('✅ New assistant created:', newAssistant.id);

    // Update Damon's attorney record with the new assistant ID
    const { error: updateError } = await supabase
      .from('attorneys')
      .update({ 
        vapi_assistant_id: newAssistant.id,
        updated_at: new Date().toISOString()
      })
      .eq('id', attorney.id);

    if (updateError) {
      console.error('❌ Error updating attorney record:', updateError);
      return;
    }

    console.log('✅ Attorney record updated with new assistant ID');

    // Update localStorage if available
    if (typeof localStorage !== 'undefined') {
      const storedAttorney = localStorage.getItem('attorney');
      if (storedAttorney) {
        const parsedAttorney = JSON.parse(storedAttorney);
        if (parsedAttorney.email === '<EMAIL>') {
          parsedAttorney.vapi_assistant_id = newAssistant.id;
          localStorage.setItem('attorney', JSON.stringify(parsedAttorney));
          console.log('📱 Updated localStorage with new assistant ID');
        }
      }
    }

    console.log('\n🎉 Conflict resolved successfully!');
    console.log('Old assistant ID:', attorney.vapi_assistant_id);
    console.log('New assistant ID:', newAssistant.id);
    console.log('Conflicting attorneys still using old ID:', conflictingAttorneys.map(a => a.email));

    return {
      success: true,
      oldAssistantId: attorney.vapi_assistant_id,
      newAssistantId: newAssistant.id,
      conflictingAttorneys
    };

  } catch (error) {
    console.error('❌ Error during fix:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

console.log('🔧 Manual fix loaded! Run: fixDamonAssistantConflict()');
