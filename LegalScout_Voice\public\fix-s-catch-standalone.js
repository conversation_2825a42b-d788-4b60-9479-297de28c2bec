/**
 * Standalone fix for S(...).catch is not a function error
 *
 * This script is a minimal fix that only targets the specific error
 * without causing recursion with other fix scripts.
 *
 * IMPORTANT: This must be the very first script loaded on the page.
 */
(function() {
  console.log('[SCatchStandalone] Starting standalone S(...).catch fix...');

  // ===== CRITICAL FIXES FOR DEVELOPMENT =====

  // 1. Fix Process Undefined Error
  if (typeof window !== 'undefined' && typeof window.process === 'undefined') {
    console.log('[SCatchStandalone] Adding process polyfill for browser environment');
    window.process = {
      env: { NODE_ENV: 'development' },
      browser: true,
      version: '',
      versions: { node: '' }
    };
    console.log('[SCatchStandalone] Process polyfill added successfully');
  }

  // 2. Detect Development Mode
  const isDevelopment = window.location.hostname === 'localhost' ||
                       window.location.hostname === '127.0.0.1' ||
                       window.location.port === '5174' ||
                       window.location.port === '5173';

  if (isDevelopment) {
    console.log('[SCatchStandalone] Development mode detected, applying URL fixes');
  }

  // Global error handler for the specific error
  window.addEventListener('error', function(event) {
    if (event.error && event.error.message && event.error.message.includes('catch is not a function')) {
      console.log('[SCatchStandalone] Caught the specific error: ' + event.error.message);
      event.preventDefault();
      return false;
    }
  }, true);

  // Add a global catch method to Promise.prototype if it doesn't exist
  if (!Promise.prototype.catch) {
    Promise.prototype.catch = function(onRejected) {
      return this.then(null, onRejected);
    };
    console.log('[SCatchStandalone] Added catch method to Promise.prototype');
  }

  // Patch the Promise.prototype.then method to ensure all promises have a catch method
  const originalThen = Promise.prototype.then;
  Promise.prototype.then = function(...args) {
    const result = originalThen.apply(this, args);

    // Ensure the result has a catch method
    if (result && typeof result.then === 'function' && typeof result.catch !== 'function') {
      console.log('[SCatchStandalone] Adding catch method to Promise.then result');
      result.catch = function(onRejected) {
        return Promise.resolve(result).catch(onRejected);
      };
    }

    return result;
  };

  // Add a global handler for unhandled promise rejections
  window.addEventListener('unhandledrejection', function(event) {
    console.log('[SCatchStandalone] Caught unhandled rejection:', event.reason);
    event.preventDefault();
  });

  // Add a global S function if it doesn't exist
  if (!window.S) {
    window.S = function(...args) {
      console.log('[SCatchStandalone] Using fallback S function');
      const result = Promise.resolve(null);
      // Ensure the result has a catch method
      if (typeof result.catch !== 'function') {
        result.catch = function(onRejected) {
          return Promise.resolve(result).catch(onRejected);
        };
      }
      return result;
    };
    console.log('[SCatchStandalone] Added fallback S function');
  } else {
    // Patch the existing S function
    const originalS = window.S;
    window.S = function(...args) {
      try {
        const result = originalS.apply(this, args);

        // Ensure the result has a catch method
        if (result && typeof result.then === 'function' && typeof result.catch !== 'function') {
          console.log('[SCatchStandalone] Adding catch method to S function result');
          result.catch = function(onRejected) {
            return Promise.resolve(result).catch(onRejected);
          };
        }

        return result;
      } catch (error) {
        console.error('[SCatchStandalone] Error in S function:', error);
        // Return a safe value
        const safeResult = Promise.resolve(null);
        safeResult.catch = function(onRejected) {
          return Promise.resolve(safeResult).catch(onRejected);
        };
        return safeResult;
      }
    };
    console.log('[SCatchStandalone] Patched existing S function');
  }

  // Add a global Qr function if it doesn't exist
  if (!window.Qr) {
    window.Qr = function(...args) {
      console.log('[SCatchStandalone] Using fallback Qr function');
      const result = Promise.resolve(null);
      // Ensure the result has a catch method
      if (typeof result.catch !== 'function') {
        result.catch = function(onRejected) {
          return Promise.resolve(result).catch(onRejected);
        };
      }
      return result;
    };
    console.log('[SCatchStandalone] Added fallback Qr function');
  } else {
    // Patch the existing Qr function
    const originalQr = window.Qr;
    window.Qr = function(...args) {
      try {
        const result = originalQr.apply(this, args);

        // Ensure the result has a catch method
        if (result && typeof result.then === 'function' && typeof result.catch !== 'function') {
          console.log('[SCatchStandalone] Adding catch method to Qr function result');
          result.catch = function(onRejected) {
            return Promise.resolve(result).catch(onRejected);
          };
        }

        return result;
      } catch (error) {
        console.error('[SCatchStandalone] Error in Qr function:', error);
        // Return a safe value
        const safeResult = Promise.resolve(null);
        safeResult.catch = function(onRejected) {
          return Promise.resolve(safeResult).catch(onRejected);
        };
        return safeResult;
      }
    };
    console.log('[SCatchStandalone] Patched existing Qr function');
  }

  // Add a global helper function to ensure objects have a catch method
  window._ensureCatchMethod = function(result) {
    if (result &&
        typeof result === 'object' &&
        typeof result.then === 'function' &&
        typeof result.catch !== 'function') {
      console.log('[SCatchStandalone] Adding catch method to object');
      result.catch = function(onRejected) {
        return Promise.resolve(result).catch(onRejected);
      };
    }
    return result;
  };

  // Patch the global fetch function to ensure all promises have a catch method AND fix URLs
  if (typeof window.fetch === 'function') {
    const originalFetch = window.fetch;
    window.fetch = function(url, options = {}) {
      try {
        let fixedUrl = url;

        // 3. Fix API URLs in Development Mode
        if (isDevelopment && typeof url === 'string') {
          // Fix production domain URLs
          if (url.includes('https://dashboard.legalscout.net')) {
            fixedUrl = url.replace('https://dashboard.legalscout.net', window.location.origin);
            console.log('[SCatchStandalone] Fixed production URL:', url, '→', fixedUrl);
          }

          // Fix malformed URLs with [object Object]
          if (url.includes('[object Object]')) {
            console.warn('[SCatchStandalone] Detected malformed URL with [object Object]:', url);
            // Try to extract the path part
            const pathMatch = url.match(/\/api\/[^\/]+/);
            if (pathMatch) {
              fixedUrl = window.location.origin + pathMatch[0];
              console.log('[SCatchStandalone] Fixed malformed URL:', url, '→', fixedUrl);
            }
          }

          // Ensure relative API URLs use the correct origin
          if (url.startsWith('/api/')) {
            fixedUrl = window.location.origin + url;
            console.log('[SCatchStandalone] Fixed relative API URL:', url, '→', fixedUrl);
          }
        }

        const result = originalFetch(fixedUrl, options);

        // Ensure the result has a catch method
        if (result && typeof result.then === 'function' && typeof result.catch !== 'function') {
          console.log('[SCatchStandalone] Adding catch method to fetch result');
          result.catch = function(onRejected) {
            return Promise.resolve(result).catch(onRejected);
          };
        }

        return result;
      } catch (error) {
        console.error('[SCatchStandalone] Error in fetch:', error);
        // Return a safe value
        const safeResult = Promise.resolve(null);
        safeResult.catch = function(onRejected) {
          return Promise.resolve(safeResult).catch(onRejected);
        };
        return safeResult;
      }
    };
    console.log('[SCatchStandalone] Patched fetch function with URL fixes');
  }

  // Patch XMLHttpRequest to ensure all promises have a catch method
  if (typeof XMLHttpRequest === 'function') {
    const originalOpen = XMLHttpRequest.prototype.open;
    XMLHttpRequest.prototype.open = function(...args) {
      try {
        const result = originalOpen.apply(this, args);

        // Add a catch method to the XMLHttpRequest object
        if (this && typeof this.then === 'function' && typeof this.catch !== 'function') {
          console.log('[SCatchStandalone] Adding catch method to XMLHttpRequest');
          this.catch = function(onRejected) {
            return Promise.resolve(this).catch(onRejected);
          };
        }

        return result;
      } catch (error) {
        console.error('[SCatchStandalone] Error in XMLHttpRequest.open:', error);
        // Return a safe value
        return undefined;
      }
    };
    console.log('[SCatchStandalone] Patched XMLHttpRequest.open function');
  }

  // Add a MutationObserver to watch for script loads
  const observer = new MutationObserver(function(mutations) {
    mutations.forEach(function(mutation) {
      if (mutation.type === 'childList') {
        mutation.addedNodes.forEach(function(node) {
          if (node.tagName === 'SCRIPT' && node.src && node.src.includes('pages-')) {
            console.log('[SCatchStandalone] Detected new script load:', node.src);
            // Wait for script to load and then check for S function
            node.addEventListener('load', function() {
              console.log('[SCatchStandalone] Script loaded, checking for S function');
              setTimeout(checkForSFunction, 500);
            });
          }
        });
      }
    });
  });

  observer.observe(document.documentElement, {
    childList: true,
    subtree: true
  });

  function checkForSFunction() {
    // Look for S function in global scope
    if (typeof window.S === 'function' && !window.S._patched) {
      console.log('[SCatchStandalone] Found global S function after script load');

      // Patch the S function
      const originalS = window.S;
      window.S = function(...args) {
        try {
          const result = originalS.apply(this, args);

          // Ensure the result has a catch method
          if (result && typeof result.then === 'function' && typeof result.catch !== 'function') {
            console.log('[SCatchStandalone] Adding catch method to S function result');
            result.catch = function(onRejected) {
              return Promise.resolve(result).catch(onRejected);
            };
          }

          return result;
        } catch (error) {
          console.error('[SCatchStandalone] Error in S function:', error);
          // Return a safe value
          const safeResult = Promise.resolve(null);
          safeResult.catch = function(onRejected) {
            return Promise.resolve(safeResult).catch(onRejected);
          };
          return safeResult;
        }
      };

      window.S._patched = true;
      console.log('[SCatchStandalone] Patched S function after script load');
    }

    // Look for Qr function in global scope
    if (typeof window.Qr === 'function' && !window.Qr._patched) {
      console.log('[SCatchStandalone] Found Qr function after script load');

      // Patch the Qr function
      const originalQr = window.Qr;
      window.Qr = function(...args) {
        try {
          const result = originalQr.apply(this, args);

          // Ensure the result has a catch method
          if (result && typeof result.then === 'function' && typeof result.catch !== 'function') {
            console.log('[SCatchStandalone] Adding catch method to Qr function result');
            result.catch = function(onRejected) {
              return Promise.resolve(result).catch(onRejected);
            };
          }

          return result;
        } catch (error) {
          console.error('[SCatchStandalone] Error in Qr function:', error);
          // Return a safe value
          const safeResult = Promise.resolve(null);
          safeResult.catch = function(onRejected) {
            return Promise.resolve(safeResult).catch(onRejected);
          };
          return safeResult;
        }
      };

      window.Qr._patched = true;
      console.log('[SCatchStandalone] Patched Qr function after script load');
    }
  }

  // Also patch form field interactions
  function patchFormFieldInteractions() {
    console.log('[SCatchStandalone] Patching form field interactions...');

    // Find all input fields
    const inputFields = document.querySelectorAll('input, textarea, select');

    inputFields.forEach(field => {
      if (field._sCatchStandalonePatched) return;

      field._sCatchStandalonePatched = true;

      // Add event listeners for common input events
      ['input', 'change', 'focus', 'blur', 'keydown', 'keyup'].forEach(eventType => {
        field.addEventListener(eventType, function(event) {
          try {
            // Wrap the event in a try-catch to prevent errors from bubbling up
            // This allows the event to proceed normally but catches any errors

            // If there's an error handler on the window, make sure it's called
            if (window._ensureCatchMethod) {
              // Ensure any promises returned have a catch method
              setTimeout(() => {
                // Look for any promises in the event path
                event.path && event.path.forEach(element => {
                  if (element && element._promise) {
                    window._ensureCatchMethod(element._promise);
                  }
                });
              }, 0);
            }
          } catch (error) {
            console.error('[SCatchStandalone] Error in form field event handler:', error);
          }
        }, true); // Use capture to ensure our handler runs first
      });

      console.log('[SCatchStandalone] Patched form field:', {
        tagName: field.tagName,
        id: field.id || 'no-id',
        className: field.className || 'no-class',
        type: field.type || 'no-type',
        name: field.name || 'no-name'
      });
    });
  }

  // Wait for the DOM to be ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', patchFormFieldInteractions);
  } else {
    patchFormFieldInteractions();
  }

  // Also run the fix when the window loads
  window.addEventListener('load', patchFormFieldInteractions);

  console.log('[SCatchStandalone] Standalone S(...).catch fix complete');
})();
