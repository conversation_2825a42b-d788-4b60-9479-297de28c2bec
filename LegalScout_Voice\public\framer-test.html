<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Framer Motion Fix Test</title>
    <!-- Direct patch for LayoutGroupContext.mjs - Must load first -->
    <script src="/layout-group-context-patch.js"></script>
    <!-- Enhanced Framer Motion Fix - Must load before any other scripts -->
    <script src="/enhanced-framer-fix.js"></script>
    <!-- Test script to verify our fix is working -->
    <script src="/test-framer-fix.js"></script>
</head>
<body>
    <h1>Framer Motion Fix Test</h1>
    <p>This page tests if our Framer Motion fix is working correctly.</p>
    <p>Check the browser console for test results.</p>

    <script>
        // Simulate the LayoutGroupContext.mjs error
        try {
            // This would normally cause the error
            const testContext = React.createContext(null);
            document.write('<p style="color: green">Success: React.createContext is working!</p>');
        } catch (error) {
            document.write('<p style="color: red">Error: ' + error.message + '</p>');
        }

        // Test if LayoutGroupContext is defined
        if (typeof window.LayoutGroupContext !== 'undefined') {
            document.write('<p style="color: green">Success: LayoutGroupContext is defined!</p>');
        } else {
            document.write('<p style="color: red">Error: LayoutGroupContext is undefined</p>');
        }
    </script>
</body>
</html>
