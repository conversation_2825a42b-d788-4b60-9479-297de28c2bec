<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Three.js Test (Fixed)</title>
</head>
<body>
    <div id="globe-container" style="width: 100%; height: 500px;"></div>
    <div id="status">Loading...</div>
    
    <script>
        // Status reporting
        function updateStatus(message) {
            document.getElementById('status').innerHTML += '<br>' + message;
            console.log(message);
        }
        
        updateStatus('Starting script loading sequence');
        
        // Create a global THREE object if it doesn't exist
        if (typeof THREE === 'undefined') {
            updateStatus('Creating global THREE object');
            window.THREE = {};
        }
        
        // Load Three.js
        function loadThreeJs() {
            return new Promise((resolve, reject) => {
                updateStatus('Loading Three.js...');
                const script = document.createElement('script');
                script.src = 'https://unpkg.com/three@0.174.0/build/three.min.js';
                script.onload = () => {
                    updateStatus('Three.js loaded successfully');
                    resolve();
                };
                script.onerror = (error) => {
                    updateStatus('Error loading Three.js: ' + error);
                    reject(error);
                };
                document.head.appendChild(script);
            });
        }
        
        // Load OrbitControls
        function loadOrbitControls() {
            return new Promise((resolve, reject) => {
                updateStatus('Loading OrbitControls...');
                const script = document.createElement('script');
                script.src = 'https://unpkg.com/three@0.174.0/examples/js/controls/OrbitControls.js';
                script.onload = () => {
                    updateStatus('OrbitControls loaded successfully');
                    resolve();
                };
                script.onerror = (error) => {
                    updateStatus('Error loading OrbitControls: ' + error);
                    reject(error);
                };
                document.head.appendChild(script);
            });
        }
        
        // Load Three Globe
        function loadThreeGlobe() {
            return new Promise((resolve, reject) => {
                updateStatus('Loading Three Globe...');
                const script = document.createElement('script');
                script.src = 'https://unpkg.com/three-globe@2.42.1/dist/three-globe.min.js';
                script.onload = () => {
                    updateStatus('Three Globe loaded successfully');
                    resolve();
                };
                script.onerror = (error) => {
                    updateStatus('Error loading Three Globe: ' + error);
                    reject(error);
                };
                document.head.appendChild(script);
            });
        }
        
        // Initialize the globe
        function initGlobe() {
            try {
                updateStatus('Initializing globe...');
                
                // Check if THREE is defined
                updateStatus('THREE defined: ' + (typeof THREE !== 'undefined'));
                
                // Check if ThreeGlobe is defined
                updateStatus('ThreeGlobe defined: ' + (typeof ThreeGlobe !== 'undefined'));
                
                // Try to create a simple scene
                try {
                    const scene = new THREE.Scene();
                    updateStatus('Scene created successfully');
                    
                    // Try to create a globe
                    try {
                        const globe = new ThreeGlobe();
                        updateStatus('Globe created successfully');
                        
                        // Create a renderer
                        const renderer = new THREE.WebGLRenderer();
                        renderer.setSize(window.innerWidth, window.innerHeight);
                        document.getElementById('globe-container').appendChild(renderer.domElement);
                        
                        updateStatus('Renderer created successfully');
                        
                        // Create a camera
                        const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
                        camera.position.z = 5;
                        
                        updateStatus('Camera created successfully');
                        
                        // Add the globe to the scene
                        scene.add(globe);
                        
                        updateStatus('Globe added to scene');
                        
                        // Render the scene
                        renderer.render(scene, camera);
                        
                        updateStatus('Scene rendered successfully');
                    } catch (globeError) {
                        updateStatus('Error creating globe: ' + globeError);
                    }
                } catch (sceneError) {
                    updateStatus('Error creating scene: ' + sceneError);
                }
            } catch (error) {
                updateStatus('Error initializing globe: ' + error);
            }
        }
        
        // Load scripts in the correct order
        async function loadScripts() {
            try {
                await loadThreeJs();
                await loadOrbitControls();
                await loadThreeGlobe();
                initGlobe();
            } catch (error) {
                updateStatus('Error loading scripts: ' + error);
            }
        }
        
        // Start loading scripts
        loadScripts();
    </script>
</body>
</html>
