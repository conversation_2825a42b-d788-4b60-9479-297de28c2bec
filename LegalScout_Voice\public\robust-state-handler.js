/**
 * Robust State Handler
 * 
 * Handles ANY state thrown at the system:
 * - No attorney record → Create one
 * - No assistants → Show create option
 * - Multiple assistants → Pull latest into dropdown
 * - Prevents out-of-control creation
 * - Handles email mismatches gracefully
 */

(function robustStateHandler() {
  // Prevent multiple initializations
  if (window.ROBUST_STATE_HANDLER_INITIALIZED) {
    console.log('🛡️ [<PERSON>ustStateHandler] Already initialized, skipping...');
    return;
  }

  console.log('🛡️ [RobustStateHandler] Initializing comprehensive state management...');

  let initializationAttempts = 0;
  const maxInitializationAttempts = 3;
  let isInitializing = false;

  // Wait for dependencies with timeout and attempt limiting
  function attemptInitialization() {
    if (isInitializing) {
      console.log('🛡️ [RobustStateHandler] Initialization already in progress, skipping...');
      return;
    }

    initializationAttempts++;
    if (initializationAttempts > maxInitializationAttempts) {
      console.error('❌ [RobustStateHandler] Max initialization attempts reached, giving up');
      return;
    }

    console.log(`🛡️ [<PERSON>ustStateHandler] Initialization attempt ${initializationAttempts}/${maxInitializationAttempts}`);

    // Wait for dependencies with more thorough checking
    let checkInterval = setInterval(() => {
      if (document.readyState === 'complete' &&
          window.supabase &&
          typeof window.supabase.from === 'function') {
        clearInterval(checkInterval);
        console.log('🔗 [RobustStateHandler] Dependencies ready, initializing...');
        isInitializing = true;
        initializeRobustHandling();
      } else {
        console.log('⏳ [RobustStateHandler] Waiting for dependencies...', {
          domReady: document.readyState === 'complete',
          supabaseExists: !!window.supabase,
          supabaseHasFrom: window.supabase && typeof window.supabase.from === 'function'
        });
      }
    }, 1000); // Increased interval to reduce log spam

    setTimeout(() => {
      clearInterval(checkInterval);
      if (!isInitializing) {
        console.warn(`[RobustStateHandler] Attempt ${initializationAttempts} timed out, trying fallback...`);

        // Try fallback initialization
        if (window.supabase) {
          console.log('🔄 [RobustStateHandler] Supabase available, trying fallback initialization...');
          isInitializing = true;
          initializeRobustHandling();
        } else {
          console.error('❌ [RobustStateHandler] Supabase not available for this attempt');
          // Try again if we haven't reached max attempts
          if (initializationAttempts < maxInitializationAttempts) {
            setTimeout(attemptInitialization, 2000);
          }
        }
      }
    }, 10000); // Reduced timeout
  }

  // Start first attempt
  attemptInitialization();
  
  function initializeRobustHandling() {
    console.log('🚀 [RobustStateHandler] Starting robust state handling...');

    // Race condition prevention - track ongoing operations
    const ongoingOperations = new Map();

    // Main state resolution function with safeguards
    window.resolveAttorneyState = async function(email) {
      console.log(`🔍 [RobustStateHandler] Resolving state for email: ${email}`);

      // 🛡️ SAFEGUARD 1: Validate input
      if (!email || typeof email !== 'string' || !email.includes('@')) {
        console.error('❌ [RobustStateHandler] Invalid email provided:', email);
        return {
          success: false,
          error: 'Invalid email address',
          attorney: null,
          assistants: [],
          needsCreation: true
        };
      }

      // 🛡️ SAFEGUARD 2: Race condition prevention
      if (ongoingOperations.has(email)) {
        console.log('⏳ [RobustStateHandler] Operation already in progress, waiting...');
        return await ongoingOperations.get(email);
      }

      // Create promise for this operation
      const operationPromise = performStateResolution(email);
      ongoingOperations.set(email, operationPromise);

      try {
        const result = await operationPromise;
        return result;
      } finally {
        // Clean up operation tracking
        ongoingOperations.delete(email);
      }
    };

    // Internal state resolution with retry logic
    async function performStateResolution(email) {
      const maxRetries = 3;
      let lastError;

      for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
          console.log(`🔄 [RobustStateHandler] Attempt ${attempt}/${maxRetries} for ${email}`);

          // Step 1: Try to find existing attorney
          let attorney = await findOrCreateAttorney(email);

          // 🛡️ SAFEGUARD 3: Validate attorney data
          if (!attorney || !attorney.id || !attorney.email) {
            throw new Error('Invalid attorney data returned');
          }

          // Step 2: Handle assistant state
          const assistantState = await resolveAssistantState(attorney);

          // Step 3: Return complete state
          return {
            success: true,
            attorney,
            assistants: assistantState.assistants,
            selectedAssistant: assistantState.selectedAssistant,
            needsCreation: assistantState.needsCreation,
            message: assistantState.message
          };

        } catch (error) {
          console.error(`❌ [RobustStateHandler] Attempt ${attempt} failed:`, error);
          lastError = error;

          // Wait before retry (exponential backoff)
          if (attempt < maxRetries) {
            const delay = Math.pow(2, attempt) * 1000; // 2s, 4s, 8s
            console.log(`⏳ [RobustStateHandler] Retrying in ${delay}ms...`);
            await new Promise(resolve => setTimeout(resolve, delay));
          }
        }
      }

      // All retries failed
      console.error('❌ [RobustStateHandler] All retries failed for:', email);
      return {
        success: false,
        error: lastError?.message || 'Unknown error',
        attorney: null,
        assistants: [],
        needsCreation: true
      };
    }
    
    // Find or create attorney record
    async function findOrCreateAttorney(email) {
      console.log(`🔍 [RobustStateHandler] Looking for attorney: ${email}`);

      // 🛡️ SAFEGUARD 8: Validate dependencies
      if (!window.supabase || typeof window.supabase.from !== 'function') {
        throw new Error('Supabase client not available');
      }

      // 🛡️ SAFEGUARD 9: Verify user is still authenticated and get user ID
      let authenticatedUser;
      try {
        const { data: { user }, error: authError } = await window.supabase.auth.getUser();
        if (authError || !user || user.email !== email) {
          throw new Error('User authentication invalid or email mismatch');
        }
        authenticatedUser = user;
      } catch (authError) {
        throw new Error(`Authentication check failed: ${authError.message}`);
      }

      // Try to find existing attorney by user_id first (most reliable)
      const { data: attorneyByUserId, error: userIdError } = await window.supabase
        .from('attorneys')
        .select('*')
        .eq('user_id', authenticatedUser.id);

      if (!userIdError && attorneyByUserId && attorneyByUserId.length > 0) {
        const attorney = attorneyByUserId[0];
        console.log('✅ [RobustStateHandler] Found existing attorney by user_id:', attorney.id);

        // 🛡️ SAFEGUARD 10: Validate attorney data integrity
        if (!attorney.id || !attorney.email || !attorney.firm_name) {
          throw new Error('Attorney record missing required fields');
        }

        return attorney;
      }

      // Fallback: Try to find existing attorney by email
      const { data: existingAttorneys, error: findError } = await window.supabase
        .from('attorneys')
        .select('*')
        .eq('email', email);

      if (findError) {
        throw new Error(`Error finding attorney: ${findError.message}`);
      }

      // If found by email, update the user_id and return
      if (existingAttorneys && existingAttorneys.length > 0) {
        if (existingAttorneys.length > 1) {
          console.warn(`[RobustStateHandler] Found ${existingAttorneys.length} attorneys for ${email}, using most recent`);
          // Sort by updated_at or created_at, use most recent
          existingAttorneys.sort((a, b) =>
            new Date(b.updated_at || b.created_at) - new Date(a.updated_at || a.created_at)
          );
        }

        const attorney = existingAttorneys[0];
        console.log('✅ [RobustStateHandler] Found existing attorney by email:', attorney.id);

        // Update user_id if it's missing or incorrect
        if (attorney.user_id !== authenticatedUser.id) {
          console.log('🔄 [RobustStateHandler] Updating attorney user_id to match authenticated user');
          const { error: updateError } = await window.supabase
            .from('attorneys')
            .update({
              user_id: authenticatedUser.id,
              updated_at: new Date().toISOString()
            })
            .eq('id', attorney.id);

          if (!updateError) {
            attorney.user_id = authenticatedUser.id;
          }
        }

        // 🛡️ SAFEGUARD 10: Validate attorney data integrity
        if (!attorney.id || !attorney.email || !attorney.firm_name) {
          throw new Error('Attorney record missing required fields');
        }

        return attorney;
      }
      
      // If not found, create a new attorney record
      console.log('📝 [RobustStateHandler] Creating new attorney record...');
      
      const newAttorney = {
        email: email,
        firm_name: extractFirmNameFromEmail(email),
        subdomain: extractSubdomainFromEmail(email),
        name: extractNameFromEmail(email),
        user_id: authenticatedUser.id, // Use the authenticated user's ID from Supabase Auth
        voice_id: 'echo',
        voice_provider: 'openai',
        ai_model: 'gpt-4o',
        vapi_instructions: `You are Scout, the AI legal assistant for ${extractFirmNameFromEmail(email)}. You help potential clients understand their legal needs and connect them with qualified attorneys.`,
        welcome_message: `Hello! I'm Scout from ${extractFirmNameFromEmail(email)}. How can I help you today?`,
        primary_color: '#4B74AA',
        secondary_color: '#2C3E50',
        button_color: '#D85722',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      
      const { data: createdAttorney, error: createError } = await window.supabase
        .from('attorneys')
        .insert([newAttorney])
        .select()
        .single();
      
      if (createError) {
        console.error('[RobustStateHandler] Error creating attorney:', createError);
        throw new Error(`Failed to create attorney: ${createError.message}`);
      }
      
      console.log('✅ [RobustStateHandler] Created new attorney:', createdAttorney.id);
      return createdAttorney;
    }
    
    // Resolve assistant state for attorney
    async function resolveAssistantState(attorney) {
      console.log(`🎯 [RobustStateHandler] Resolving assistant state for attorney: ${attorney.id}`);

      // Get all assistants for this attorney (if any)
      const assistants = await getAttorneyAssistants(attorney);

      if (assistants.length === 0) {
        console.log('📝 [RobustStateHandler] No assistants found - auto-creating default assistant');

        // Auto-create default assistant for new accounts
        const newAssistant = await createDefaultAssistant(attorney);

        if (newAssistant) {
          console.log('✅ [RobustStateHandler] Default assistant created:', newAssistant.id);

          // Update attorney record with new assistant ID
          await updateAttorneyAssistantId(attorney.id, newAssistant.id);

          // Update the attorney object to include the new assistant ID
          attorney.vapi_assistant_id = newAssistant.id;

          return {
            assistants: [newAssistant],
            selectedAssistant: newAssistant,
            needsCreation: false,
            message: 'Default assistant created and ready to use.'
          };
        } else {
          console.error('❌ [RobustStateHandler] Failed to create default assistant');
          return {
            assistants: [],
            selectedAssistant: null,
            needsCreation: true,
            message: 'Failed to create assistant. Please try again.'
          };
        }
      }

      if (assistants.length === 1) {
        console.log('✅ [RobustStateHandler] Found 1 assistant - using it');
        return {
          assistants: assistants,
          selectedAssistant: assistants[0],
          needsCreation: false,
          message: 'Assistant loaded successfully.'
        };
      }

      // Multiple assistants - use the latest one
      const sortedAssistants = assistants.sort((a, b) =>
        new Date(b.updatedAt || b.createdAt) - new Date(a.updatedAt || a.createdAt)
      );

      console.log(`📋 [RobustStateHandler] Found ${assistants.length} assistants - using latest`);
      return {
        assistants: sortedAssistants,
        selectedAssistant: sortedAssistants[0],
        needsCreation: false,
        message: `Found ${assistants.length} assistants. Using the most recent one.`
      };
    }
    
    // Get assistants for attorney (from Vapi or database)
    async function getAttorneyAssistants(attorney) {
      const assistants = [];

      // 🛡️ SAFEGUARD 4: Validate assistant ID format
      if (attorney.vapi_assistant_id && isValidAssistantId(attorney.vapi_assistant_id)) {
        try {
          const assistant = await getVapiAssistant(attorney.vapi_assistant_id);
          if (assistant && assistant.id) {
            assistants.push(assistant);
          } else {
            console.warn('[RobustStateHandler] Assistant ID exists but assistant not found in Vapi:', attorney.vapi_assistant_id);
            // Clean up orphaned assistant ID
            await cleanupOrphanedAssistantId(attorney.id, attorney.vapi_assistant_id);
          }
        } catch (error) {
          console.warn('[RobustStateHandler] Error getting Vapi assistant:', error);
          // If assistant doesn't exist, clean up the reference
          if (error.message?.includes('404') || error.message?.includes('not found')) {
            await cleanupOrphanedAssistantId(attorney.id, attorney.vapi_assistant_id);
          }
        }
      } else if (attorney.vapi_assistant_id) {
        console.warn('[RobustStateHandler] Invalid assistant ID format:', attorney.vapi_assistant_id);
        // Clean up invalid assistant ID
        await cleanupOrphanedAssistantId(attorney.id, attorney.vapi_assistant_id);
      }

      return assistants;
    }

    // Validate assistant ID format
    function isValidAssistantId(assistantId) {
      if (!assistantId || typeof assistantId !== 'string') return false;
      if (assistantId.includes('MOCK') || assistantId.includes('mock')) return false;
      // Check if it's a valid UUID format
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
      return uuidRegex.test(assistantId);
    }

    // Clean up orphaned assistant ID
    async function cleanupOrphanedAssistantId(attorneyId, orphanedId) {
      try {
        console.log('[RobustStateHandler] Cleaning up orphaned assistant ID:', orphanedId);
        await window.supabase
          .from('attorneys')
          .update({
            vapi_assistant_id: null,
            updated_at: new Date().toISOString()
          })
          .eq('id', attorneyId);
        console.log('✅ [RobustStateHandler] Orphaned assistant ID cleaned up');
      } catch (error) {
        console.error('[RobustStateHandler] Error cleaning up orphaned assistant ID:', error);
      }
    }
    
    // Get assistant from Vapi
    async function getVapiAssistant(assistantId) {
      try {
        // Try MCP service first
        if (window.vapiMcpService) {
          await window.vapiMcpService.ensureConnection();
          return await window.vapiMcpService.getAssistant(assistantId);
        }
        
        // Fallback to direct API
        const apiKey = '6734febc-fc65-4669-93b0-929b31ff6564';
        const response = await fetch(`https://api.vapi.ai/assistant/${assistantId}`, {
          headers: {
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json'
          }
        });
        
        if (response.ok) {
          return await response.json();
        }
        
        return null;
      } catch (error) {
        console.warn('[RobustStateHandler] Error fetching assistant:', error);
        return null;
      }
    }
    
    // Utility functions
    function extractFirmNameFromEmail(email) {
      // Extract firm name from email
      const username = email.split('@')[0];
      if (username.includes('damon')) return 'LegalScout';
      return username.charAt(0).toUpperCase() + username.slice(1) + ' Law Firm';
    }
    
    function extractSubdomainFromEmail(email) {
      const username = email.split('@')[0];
      return username.toLowerCase().replace(/[^a-z0-9]/g, '');
    }
    
    function extractNameFromEmail(email) {
      const username = email.split('@')[0];
      if (username.includes('damon')) return 'Damon Kost';
      return username.charAt(0).toUpperCase() + username.slice(1);
    }
    
    function generateUUID() {
      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        const r = Math.random() * 16 | 0;
        const v = c == 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
      });
    }
    
    // Create default assistant with comprehensive configuration
    async function createDefaultAssistant(attorney) {
      console.log('🎯 [RobustStateHandler] Creating default assistant for:', attorney.firm_name);

      try {
        // 🛡️ SAFEGUARD 5: Validate attorney data before creating assistant
        if (!attorney.firm_name || !attorney.email) {
          throw new Error('Attorney missing required fields for assistant creation');
        }

        // Sanitize firm name for assistant name
        const sanitizedFirmName = sanitizeText(attorney.firm_name);
        if (!sanitizedFirmName) {
          throw new Error('Invalid firm name for assistant creation');
        }

        // Create comprehensive default assistant configuration
        const assistantConfig = {
          name: `${sanitizedFirmName} Legal Assistant`,
          firstMessage: `Hello! I'm Scout from ${sanitizedFirmName}. How can I help you today?`,
          firstMessageMode: "assistant-speaks-first",

          // Model configuration with comprehensive system prompt
          model: {
            provider: "openai",
            model: "gpt-4o",
            temperature: 0.7,
            messages: [
              {
                role: "system",
                content: `You are Scout, the AI legal assistant for ${sanitizedFirmName}. You help potential clients understand their legal needs and connect them with qualified attorneys. You guide users through initial legal consultations and collect relevant information.

[Legal Consultation Process]
1. Greet warmly and ask how you can help with their legal matter
2. Listen to their legal concern and ask clarifying questions
3. Collect relevant case details, timeline, and contact information
4. Provide general legal guidance (not specific legal advice)
5. Explain next steps for connecting with an attorney

[Information to Collect]
- Client contact information (name, email, phone)
- Nature of legal issue and practice area
- Timeline and urgency of the matter
- Key facts and circumstances
- Previous legal action taken
- Desired outcome or goals
- Budget considerations if appropriate

[Communication Guidelines]
- Be professional, empathetic, and helpful
- Ask one question at a time to avoid overwhelming clients
- Clarify complex legal terms in simple language
- Never provide specific legal advice - only general information
- Always emphasize the importance of consulting with a qualified attorney

[Completion]
Conclude with: "Thank you! I have all the details I need to create your case file. ${sanitizedFirmName} is now working to find an attorney interested in your case. You'll receive an update soon."

Note: Always ensure to gather all necessary contact information before proceeding to inform the user about finding an attorney.`
              }
            ]
          },

          // Voice configuration (default to echo)
          voice: {
            provider: "openai",
            voiceId: "echo",
            speed: 1.0
          },

          // Transcriber configuration
          transcriber: {
            provider: "deepgram",
            model: "nova-3",
            language: "en"
          }
        };

        // 🛡️ SAFEGUARD 6: Validate configuration before sending to Vapi
        if (!validateAssistantConfig(assistantConfig)) {
          throw new Error('Invalid assistant configuration generated');
        }

        // Create assistant using MCP service or direct API with retry
        const assistant = await createVapiAssistantWithRetry(assistantConfig);

        if (assistant && assistant.id && isValidAssistantId(assistant.id)) {
          console.log('✅ [RobustStateHandler] Default assistant created successfully:', assistant.id);
          return assistant;
        } else {
          throw new Error('Assistant creation returned invalid response');
        }

      } catch (error) {
        console.error('❌ [RobustStateHandler] Error creating default assistant:', error);
        return null;
      }
    }

    // Sanitize text for Vapi API
    function sanitizeText(text) {
      if (!text || typeof text !== 'string') return '';
      // Remove potentially problematic characters, limit length
      return text.trim().substring(0, 100).replace(/[<>\"'&]/g, '');
    }

    // Validate assistant configuration
    function validateAssistantConfig(config) {
      if (!config || typeof config !== 'object') return false;
      if (!config.name || typeof config.name !== 'string') return false;
      if (!config.firstMessage || typeof config.firstMessage !== 'string') return false;
      if (!config.model || !config.model.provider || !config.model.model) return false;
      if (!config.voice || !config.voice.provider || !config.voice.voiceId) return false;
      return true;
    }

    // Create Vapi assistant with retry logic
    async function createVapiAssistantWithRetry(config) {
      const maxRetries = 3;
      let lastError;

      for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
          console.log(`🔄 [RobustStateHandler] Creating assistant attempt ${attempt}/${maxRetries}`);

          const assistant = await createVapiAssistant(config);

          if (assistant && assistant.id) {
            return assistant;
          } else {
            throw new Error('Invalid assistant response');
          }

        } catch (error) {
          console.error(`❌ [RobustStateHandler] Assistant creation attempt ${attempt} failed:`, error);
          lastError = error;

          // Wait before retry
          if (attempt < maxRetries) {
            const delay = Math.pow(2, attempt) * 1000; // 2s, 4s, 8s
            console.log(`⏳ [RobustStateHandler] Retrying assistant creation in ${delay}ms...`);
            await new Promise(resolve => setTimeout(resolve, delay));
          }
        }
      }

      throw lastError || new Error('Assistant creation failed after all retries');
    }

    // Create Vapi assistant using available service
    async function createVapiAssistant(config) {
      try {
        // Try MCP service first
        if (window.vapiMcpService) {
          await window.vapiMcpService.ensureConnection();
          return await window.vapiMcpService.createAssistant(config);
        }

        // Fallback to direct API
        const apiKey = '6734febc-fc65-4669-93b0-929b31ff6564';
        const response = await fetch('https://api.vapi.ai/assistant', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(config)
        });

        if (response.ok) {
          const assistant = await response.json();

          // 🛡️ SAFEGUARD 7: Validate response from Vapi
          if (!assistant || !assistant.id || !isValidAssistantId(assistant.id)) {
            throw new Error('Invalid assistant returned from Vapi API');
          }

          return assistant;
        } else {
          const errorText = await response.text();
          throw new Error(`API error: ${response.status} ${response.statusText} - ${errorText}`);
        }

      } catch (error) {
        console.error('[RobustStateHandler] Error creating Vapi assistant:', error);
        throw error;
      }
    }

    // Update attorney record with assistant ID
    async function updateAttorneyAssistantId(attorneyId, assistantId) {
      try {
        const { error } = await window.supabase
          .from('attorneys')
          .update({
            vapi_assistant_id: assistantId,
            updated_at: new Date().toISOString()
          })
          .eq('id', attorneyId);

        if (error) {
          console.error('[RobustStateHandler] Error updating attorney assistant ID:', error);
          return false;
        }

        console.log('✅ [RobustStateHandler] Attorney updated with assistant ID:', assistantId);
        return true;

      } catch (error) {
        console.error('[RobustStateHandler] Exception updating attorney:', error);
        return false;
      }
    }

    // Enhanced assistant creation that prevents duplicates
    window.createControlledAssistant = async function(attorney) {
      console.log('🎯 [RobustStateHandler] Creating controlled assistant...');

      // Double-check we don't already have an assistant
      const currentState = await resolveAssistantState(attorney);
      if (!currentState.needsCreation) {
        console.log('⚠️ [RobustStateHandler] Assistant already exists, skipping creation');
        return {
          success: true,
          assistant: currentState.selectedAssistant,
          message: 'Assistant already exists'
        };
      }

      // Create default assistant
      const newAssistant = await createDefaultAssistant(attorney);

      if (newAssistant) {
        return {
          success: true,
          assistant: newAssistant,
          message: 'Assistant created successfully'
        };
      } else {
        return {
          success: false,
          error: 'Failed to create assistant'
        };
      }
    };

    console.log('✅ [RobustStateHandler] Robust state handling initialized');

    // Mark as initialized to prevent multiple runs
    window.ROBUST_STATE_HANDLER_INITIALIZED = true;
  }
})();
