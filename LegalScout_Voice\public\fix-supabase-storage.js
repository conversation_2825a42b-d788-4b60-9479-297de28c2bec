/**
 * Fix for Supabase Storage RLS issues in development
 * 
 * This script intercepts Supabase storage calls in development mode
 * and simulates successful responses to avoid RLS policy errors.
 */

(function() {
  console.log('[StorageFix] Initializing Supabase storage fix');
  
  // Check if we're in development mode
  const isDev = window.location.hostname === 'localhost' || 
                window.location.hostname === '127.0.0.1';
  
  if (!isDev) {
    console.log('[StorageFix] Not in development mode, skipping fix');
    return;
  }
  
  // Wait for Supabase to be initialized
  const checkSupabase = setInterval(() => {
    if (window.supabase) {
      clearInterval(checkSupabase);
      applyStorageFix();
    }
  }, 100);
  
  function applyStorageFix() {
    console.log('[StorageFix] Applying Supabase storage fix');
    
    // Store original methods
    const originalUpload = window.supabase.storage.from().upload;
    const originalGetPublicUrl = window.supabase.storage.from().getPublicUrl;
    
    // Override the upload method
    window.supabase.storage.from = function(bucketName) {
      const bucket = originalUpload.call(this, bucketName);
      
      // Override the upload method
      const originalBucketUpload = bucket.upload;
      bucket.upload = function(path, fileBody, options) {
        console.log(`[StorageFix] Intercepted upload to ${bucketName}/${path}`);
        
        // In development mode, create a fake successful response
        if (isDev) {
          console.log('[StorageFix] Development mode: Simulating successful upload');
          
          // Create a local URL for the file
          let localUrl = '';
          if (fileBody instanceof File || fileBody instanceof Blob) {
            localUrl = URL.createObjectURL(fileBody);
          }
          
          // Store in localStorage for persistence
          try {
            const storageKey = `supabase_storage_${bucketName}_${path}`;
            localStorage.setItem(storageKey, localUrl);
          } catch (error) {
            console.warn('[StorageFix] Could not store file in localStorage:', error);
          }
          
          // Return a successful response
          return Promise.resolve({
            data: {
              path: path,
              id: 'dev-' + Date.now(),
              fullPath: `${bucketName}/${path}`
            },
            error: null
          });
        }
        
        // Otherwise, call the original method
        return originalBucketUpload.call(this, path, fileBody, options);
      };
      
      // Override the getPublicUrl method
      const originalBucketGetPublicUrl = bucket.getPublicUrl;
      bucket.getPublicUrl = function(path) {
        console.log(`[StorageFix] Intercepted getPublicUrl for ${bucketName}/${path}`);
        
        // In development mode, return a fake URL
        if (isDev) {
          console.log('[StorageFix] Development mode: Simulating public URL');
          
          // Try to get from localStorage first
          const storageKey = `supabase_storage_${bucketName}_${path}`;
          const storedUrl = localStorage.getItem(storageKey);
          
          if (storedUrl) {
            return {
              data: {
                publicUrl: storedUrl
              }
            };
          }
          
          // Fallback to a default URL
          return {
            data: {
              publicUrl: '/PRIMARY CLEAR.png'
            }
          };
        }
        
        // Otherwise, call the original method
        return originalBucketGetPublicUrl.call(this, path);
      };
      
      return bucket;
    };
    
    console.log('[StorageFix] Supabase storage fix applied');
  }
})();
