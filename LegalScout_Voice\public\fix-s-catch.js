/**
 * Fix for S(...).catch is not a function error
 *
 * This script specifically targets the S function that's causing the error
 * and ensures its result has a catch method.
 *
 * IMPORTANT: This version avoids recursion with fix-promise-catch.js
 */
(function() {
  console.log('[SCatchFix] Starting S(...).catch fix...');

  // Immediately apply a global error handler for the specific error
  window.addEventListener('error', function(event) {
    if (event.error && event.error.message && event.error.message.includes('catch is not a function')) {
      console.log('[SCatchFix] Caught the specific error: ' + event.error.message);
      event.preventDefault();
      return false;
    }
  }, true);

  // Store original methods without overriding them directly
  // This avoids recursion with fix-promise-catch.js
  const _originalCall = Function.prototype.call;
  const _originalApply = Function.prototype.apply;

  // Method 1: Add a global Promise-like object for S
  if (!window.S) {
    window.S = function(...args) {
      console.log('[SCatchFix] Using fallback S function');
      return Promise.resolve(null);
    };
    console.log('[SCatchFix] Added fallback S function');
  }

  // Method 2: Add a global error handler for unhandled promise rejections
  window.addEventListener('unhandledrejection', function(event) {
    console.log('[SCatchFix] Caught unhandled rejection:', event.reason);
    event.preventDefault();
  });

  // Method 3: Add a global catch method to Promise.prototype if it doesn't exist
  if (!Promise.prototype.catch) {
    Promise.prototype.catch = function(onRejected) {
      return this.then(null, onRejected);
    };
    console.log('[SCatchFix] Added catch method to Promise.prototype');
  }

  // Method 4: Add a global catch method to Object.prototype for any object that has then but not catch
  // This is a last resort and could cause issues, but it's better than the app crashing
  const originalThen = Object.prototype.then;
  if (!Object.prototype.catch && !originalThen) {
    Object.defineProperty(Object.prototype, 'catch', {
      configurable: true,
      enumerable: false,
      writable: true,
      value: function(onRejected) {
        if (this && typeof this.then === 'function') {
          return Promise.resolve(this).catch(onRejected);
        }
        return this;
      }
    });
    console.log('[SCatchFix] Added catch method to Object.prototype for thenable objects');
  }

  // Method 5: Patch the window.onerror handler
  const originalOnError = window.onerror;
  window.onerror = function(message, source, lineno, colno, error) {
    if (message && message.toString().includes('catch is not a function')) {
      console.log('[SCatchFix] Caught error in window.onerror:', message);
      return true; // Prevent default handling
    }

    // Call the original handler if it exists
    if (typeof originalOnError === 'function') {
      return originalOnError.apply(this, arguments);
    }

    return false;
  };

  // Method 6: Add a MutationObserver to watch for script loads and patch them
  const observer = new MutationObserver(function(mutations) {
    mutations.forEach(function(mutation) {
      if (mutation.type === 'childList') {
        mutation.addedNodes.forEach(function(node) {
          if (node.tagName === 'SCRIPT' && node.src && node.src.includes('pages-')) {
            console.log('[SCatchFix] Detected new script load:', node.src);
            // Wait for script to load and then check for S function
            node.addEventListener('load', function() {
              console.log('[SCatchFix] Script loaded, checking for S function');
              setTimeout(checkForSFunction, 500);
            });
          }
        });
      }
    });
  });

  observer.observe(document.documentElement, {
    childList: true,
    subtree: true
  });

  function checkForSFunction() {
    // Look for S function in global scope and common namespaces
    if (typeof window.S === 'function') {
      console.log('[SCatchFix] Found global S function after script load');
      patchSFunction(window.S);
    }

    if (typeof window.Qr === 'function') {
      console.log('[SCatchFix] Found Qr function after script load');
      patchSFunction(window.Qr);
    }

    // Look in common namespaces
    ['Supabase', 'API', 'Client', 'Utils', 'Helpers', 'Services'].forEach(namespace => {
      if (window[namespace] && typeof window[namespace].S === 'function') {
        console.log(`[SCatchFix] Found S function in ${namespace} after script load`);
        patchSFunction(window[namespace].S);
      }
    });
  }

  function patchSFunction(func) {
    if (!func || typeof func !== 'function') return;

    // We don't replace the function, we just add a property to it
    func._patched = true;

    // Add a catch method to the function's prototype if it returns a thenable
    const originalFunc = func;

    // We don't override the function to avoid recursion
    // Instead, we add a property that our error handler can check
    func._ensureCatch = function(result) {
      if (result && typeof result.then === 'function' && typeof result.catch !== 'function') {
        console.log('[SCatchFix] Adding catch method to result');
        result.catch = function(onRejected) {
          return Promise.resolve(result).catch(onRejected);
        };
      }
      return result;
    };
  }

  console.log('[SCatchFix] S(...).catch fix complete');
})();
