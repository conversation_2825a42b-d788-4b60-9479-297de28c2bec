/**
 * Test Attorney Validation
 * 
 * This script tests the validateAttorneyData method in the StandaloneAttorneyManager class.
 */

(function() {
  console.log('[TestAttorneyValidation] Starting test...');
  
  // Wait for the StandaloneAttorneyManager to be initialized
  const checkManager = setInterval(() => {
    if (window.standaloneAttorneyManager) {
      clearInterval(checkManager);
      runTests();
    }
  }, 100);
  
  function runTests() {
    const manager = window.standaloneAttorneyManager;
    
    console.log('[TestAttorneyValidation] StandaloneAttorneyManager found, running tests...');
    
    // Test 1: Validate null attorney
    console.log('[TestAttorneyValidation] Test 1: Validate null attorney');
    try {
      const validatedAttorney = manager.validateAttorneyData(null);
      console.log('[TestAttorneyValidation] Test 1 result:', validatedAttorney ? 'PASS' : 'FAIL');
    } catch (error) {
      console.error('[TestAttorneyValidation] Test 1 error:', error);
    }
    
    // Test 2: Validate attorney with invalid ID
    console.log('[TestAttorneyValidation] Test 2: Validate attorney with invalid ID');
    try {
      const invalidAttorney = {
        id: 'invalid-id',
        name: 'Test Attorney'
      };
      const validatedAttorney = manager.validateAttorneyData(invalidAttorney);
      console.log('[TestAttorneyValidation] Test 2 result:', validatedAttorney && validatedAttorney.id !== 'invalid-id' ? 'PASS' : 'FAIL');
    } catch (error) {
      console.error('[TestAttorneyValidation] Test 2 error:', error);
    }
    
    // Test 3: Validate attorney with valid ID but missing fields
    console.log('[TestAttorneyValidation] Test 3: Validate attorney with valid ID but missing fields');
    try {
      const incompleteAttorney = {
        id: '123e4567-e89b-12d3-a456-426614174000',
        name: 'Test Attorney'
      };
      const validatedAttorney = manager.validateAttorneyData(incompleteAttorney);
      console.log('[TestAttorneyValidation] Test 3 result:', 
        validatedAttorney && 
        validatedAttorney.id === '123e4567-e89b-12d3-a456-426614174000' && 
        validatedAttorney.firm_name && 
        validatedAttorney.voice_provider ? 'PASS' : 'FAIL');
    } catch (error) {
      console.error('[TestAttorneyValidation] Test 3 error:', error);
    }
    
    // Test 4: Validate complete attorney
    console.log('[TestAttorneyValidation] Test 4: Validate complete attorney');
    try {
      const completeAttorney = {
        id: '123e4567-e89b-12d3-a456-426614174000',
        name: 'Test Attorney',
        firm_name: 'Test Firm',
        voice_provider: 'test-provider',
        voice_id: 'test-voice',
        welcome_message: 'Test welcome message',
        vapi_instructions: 'Test instructions'
      };
      const validatedAttorney = manager.validateAttorneyData(completeAttorney);
      console.log('[TestAttorneyValidation] Test 4 result:', 
        validatedAttorney && 
        validatedAttorney.id === '123e4567-e89b-12d3-a456-426614174000' && 
        validatedAttorney.name === 'Test Attorney' &&
        validatedAttorney.firm_name === 'Test Firm' ? 'PASS' : 'FAIL');
    } catch (error) {
      console.error('[TestAttorneyValidation] Test 4 error:', error);
    }
    
    console.log('[TestAttorneyValidation] All tests completed');
  }
})();
