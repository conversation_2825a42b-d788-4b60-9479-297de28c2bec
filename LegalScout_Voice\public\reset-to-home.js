/**
 * Reset LegalScout to Home Page
 * 
 * This script completely resets the app to show the normal home page
 * by clearing all subdomain-related localStorage and forcing a reload.
 */

console.log('🔧 Resetting LegalScout to home page...');

// Clear ALL localStorage keys that might affect subdomain detection
const keysToRemove = [
  'legalscout_test_subdomain',
  'testSubdomain', 
  'attorney_id',
  'currentAttorneyId',
  'attorney',
  'subdomain'
];

let clearedKeys = [];

// Remove specific keys
keysToRemove.forEach(key => {
  if (localStorage.getItem(key)) {
    localStorage.removeItem(key);
    clearedKeys.push(key);
  }
});

// Also remove any keys that contain subdomain-related terms
Object.keys(localStorage).forEach(key => {
  if (key.includes('legal') || 
      key.includes('subdomain') || 
      key.includes('attorney') ||
      key.includes('test')) {
    localStorage.removeItem(key);
    if (!clearedKeys.includes(key)) {
      clearedKeys.push(key);
    }
  }
});

console.log(`✅ Cleared ${clearedKeys.length} localStorage keys:`, clearedKeys);

// Force reload to apply changes
console.log('🔄 Reloading page...');
window.location.href = 'http://localhost:5173/';
