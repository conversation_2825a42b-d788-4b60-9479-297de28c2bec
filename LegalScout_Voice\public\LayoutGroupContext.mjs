// Mock implementation of LayoutGroupContext.mjs
console.log('[MockFile] Using mock LayoutGroupContext.mjs file');

// Create a simple object instead of using React.createContext
const LayoutGroupContext = {
  Provider: function(props) { return props.children || null; },
  Consumer: function(props) { return props.children ? props.children({}) : null; },
  displayName: 'LayoutGroupContext'
};

export { LayoutGroupContext };
export default LayoutGroupContext;
