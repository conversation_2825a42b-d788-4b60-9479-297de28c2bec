<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Attorney Creation Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        h1, h2 {
            color: #333;
        }
        button {
            background-color: #4B74AA;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #3A5A8C;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            max-height: 300px;
            overflow-y: auto;
        }
        input, textarea {
            width: 100%;
            padding: 8px;
            margin-bottom: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        textarea {
            height: 100px;
        }
        .log-container {
            margin-top: 20px;
        }
    </style>
    <!-- Include the fix scripts -->
    <script src="/fix-attorney-creation.js"></script>
    <script src="/fix-attorney-persistence.js"></script>
    <script src="/sync-tools-fix.js"></script>
    <script src="/fix-auth-state.js"></script>
    
    <!-- Add Supabase meta tags -->
    <meta name="supabase-url" content="https://utopqxsvudgrtiwenlzl.supabase.co">
    <meta name="supabase-anon-key" content="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MTM5MDc0MDcsImV4cCI6MjAyOTQ4MzQwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU">
</head>
<body>
    <h1>Attorney Creation Test</h1>
    
    <div class="section">
        <h2>Current Attorney Data</h2>
        <pre id="currentAttorney">Loading...</pre>
        <button id="refreshAttorney">Refresh</button>
        <button id="clearAttorney">Clear Attorney Data</button>
    </div>
    
    <div class="section">
        <h2>Current Auth Data</h2>
        <pre id="currentAuth">Loading...</pre>
        <button id="refreshAuth">Refresh</button>
        <button id="clearAuth">Clear Auth Data</button>
    </div>
    
    <div class="section">
        <h2>Create Mock Auth</h2>
        <div>
            <label for="userId">User ID:</label>
            <input type="text" id="userId" placeholder="UUID or leave blank for auto-generated">
        </div>
        <div>
            <label for="userEmail">Email:</label>
            <input type="email" id="userEmail" value="<EMAIL>">
        </div>
        <div>
            <label for="userName">Name:</label>
            <input type="text" id="userName" value="Test User">
        </div>
        <button id="createMockAuth">Create Mock Auth</button>
    </div>
    
    <div class="section">
        <h2>Attorney Creation</h2>
        <button id="ensureAttorneyExists">Ensure Attorney Exists</button>
        <button id="checkAttorneyInSupabase">Check Attorney in Supabase</button>
    </div>
    
    <div class="section">
        <h2>Log</h2>
        <div class="log-container">
            <pre id="log"></pre>
        </div>
        <button id="clearLog">Clear Log</button>
    </div>
    
    <script>
        // Helper function to log messages
        function log(message) {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toISOString().split('T')[1].split('.')[0];
            logElement.textContent = `[${timestamp}] ${message}\n` + logElement.textContent;
        }
        
        // Helper function to get attorney data
        function getAttorneyData() {
            try {
                const storedAttorney = localStorage.getItem('attorney');
                if (storedAttorney) {
                    return JSON.parse(storedAttorney);
                }
            } catch (error) {
                log(`Error getting attorney data: ${error.message}`);
            }
            return null;
        }
        
        // Helper function to get auth data
        function getAuthData() {
            try {
                const authData = localStorage.getItem('supabase.auth.token');
                if (authData) {
                    return JSON.parse(authData);
                }
            } catch (error) {
                log(`Error getting auth data: ${error.message}`);
            }
            return null;
        }
        
        // Helper function to generate a UUID
        function generateUUID() {
            return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
                const r = Math.random() * 16 | 0;
                const v = c === 'x' ? r : (r & 0x3 | 0x8);
                return v.toString(16);
            });
        }
        
        // Update the attorney display
        function updateAttorneyDisplay() {
            const attorneyElement = document.getElementById('currentAttorney');
            const attorney = getAttorneyData();
            
            if (attorney) {
                attorneyElement.textContent = JSON.stringify(attorney, null, 2);
            } else {
                attorneyElement.textContent = 'No attorney data found';
            }
        }
        
        // Update the auth display
        function updateAuthDisplay() {
            const authElement = document.getElementById('currentAuth');
            const auth = getAuthData();
            
            if (auth) {
                // Only show relevant parts of the auth data
                const simplifiedAuth = {
                    currentSession: auth.currentSession ? {
                        user: auth.currentSession.user ? {
                            id: auth.currentSession.user.id,
                            email: auth.currentSession.user.email,
                            user_metadata: auth.currentSession.user.user_metadata
                        } : null
                    } : null
                };
                
                authElement.textContent = JSON.stringify(simplifiedAuth, null, 2);
            } else {
                authElement.textContent = 'No auth data found';
            }
        }
        
        // Initialize displays
        updateAttorneyDisplay();
        updateAuthDisplay();
        
        // Set up event listeners
        document.getElementById('refreshAttorney').addEventListener('click', () => {
            updateAttorneyDisplay();
            log('Attorney data refreshed');
        });
        
        document.getElementById('clearAttorney').addEventListener('click', () => {
            localStorage.removeItem('attorney');
            updateAttorneyDisplay();
            log('Attorney data cleared');
        });
        
        document.getElementById('refreshAuth').addEventListener('click', () => {
            updateAuthDisplay();
            log('Auth data refreshed');
        });
        
        document.getElementById('clearAuth').addEventListener('click', () => {
            localStorage.removeItem('supabase.auth.token');
            updateAuthDisplay();
            log('Auth data cleared');
        });
        
        document.getElementById('createMockAuth').addEventListener('click', () => {
            const userId = document.getElementById('userId').value.trim() || generateUUID();
            const email = document.getElementById('userEmail').value.trim();
            const name = document.getElementById('userName').value.trim();
            
            const mockAuth = {
                currentSession: {
                    user: {
                        id: userId,
                        email: email,
                        user_metadata: {
                            name: name
                        }
                    }
                }
            };
            
            localStorage.setItem('supabase.auth.token', JSON.stringify(mockAuth));
            updateAuthDisplay();
            log(`Created mock auth with user ID: ${userId}`);
            
            // Dispatch auth state change event
            window.dispatchEvent(new CustomEvent('supabase.auth.statechange'));
        });
        
        document.getElementById('ensureAttorneyExists').addEventListener('click', () => {
            if (typeof window.ensureAttorneyExists === 'function') {
                log('Ensuring attorney exists...');
                window.ensureAttorneyExists()
                    .then(attorney => {
                        if (attorney) {
                            log(`Attorney exists: ${attorney.id}`);
                        } else {
                            log('No attorney returned');
                        }
                        updateAttorneyDisplay();
                    })
                    .catch(error => {
                        log(`Error ensuring attorney exists: ${error.message}`);
                    });
            } else {
                log('ensureAttorneyExists function not found');
            }
        });
        
        document.getElementById('checkAttorneyInSupabase').addEventListener('click', async () => {
            const auth = getAuthData();
            if (!auth || !auth.currentSession || !auth.currentSession.user || !auth.currentSession.user.id) {
                log('No authenticated user found');
                return;
            }
            
            const userId = auth.currentSession.user.id;
            log(`Checking for attorney with ID: ${userId}`);
            
            // Get Supabase URL and key from meta tags
            const supabaseUrl = document.querySelector('meta[name="supabase-url"]')?.content;
            const supabaseKey = document.querySelector('meta[name="supabase-anon-key"]')?.content;
            
            if (!supabaseUrl || !supabaseKey) {
                log('Supabase URL or key not found in meta tags');
                return;
            }
            
            try {
                const response = await fetch(`${supabaseUrl}/rest/v1/attorneys?select=*&id=eq.${userId}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'apikey': supabaseKey,
                        'Authorization': `Bearer ${supabaseKey}`
                    }
                });
                
                if (!response.ok) {
                    log(`Error checking for attorney: ${await response.text()}`);
                    return;
                }
                
                const attorneys = await response.json();
                
                if (attorneys && attorneys.length > 0) {
                    log(`Attorney found in Supabase: ${JSON.stringify(attorneys[0])}`);
                } else {
                    log('No attorney found in Supabase');
                }
            } catch (error) {
                log(`Error checking attorney in Supabase: ${error.message}`);
            }
        });
        
        document.getElementById('clearLog').addEventListener('click', () => {
            document.getElementById('log').textContent = '';
            log('Log cleared');
        });
        
        // Initial log
        log('Attorney Creation Test Page Loaded');
    </script>
</body>
</html>
