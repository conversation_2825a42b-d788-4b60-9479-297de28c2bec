/**
 * Direct Patch for Framer Motion Context Issues
 * 
 * This script directly patches the framer-motion modules that cause issues
 * by intercepting their imports and replacing them with our fixed versions.
 */

(function() {
  console.log('[DirectPatch] Applying direct patch for framer-motion');

  // STEP 1: Create global React object if it doesn't exist
  if (typeof window.React === 'undefined') {
    console.log('[DirectPatch] React not found, creating global object');
    window.React = {};
  }

  // STEP 2: Define createContext if it doesn't exist
  if (typeof window.React.createContext === 'undefined') {
    console.log('[DirectPatch] createContext not found, creating polyfill');
    window.React.createContext = function(defaultValue) {
      console.log('[DirectPatch] Using polyfill createContext');
      return {
        Provider: function(props) { return props.children || null; },
        Consumer: function(props) { return props.children ? props.children(defaultValue) : null; },
        displayName: 'MockContext',
        _currentValue: defaultValue,
        _currentValue2: defaultValue,
        _threadCount: 0,
        _defaultValue: defaultValue
      };
    };
  }

  // STEP 3: Create our fixed context objects
  const createFixedContext = (name, defaultValue) => {
    console.log(`[DirectPatch] Creating fixed ${name}`);
    return {
      Provider: function(props) { return props.children || null; },
      Consumer: function(props) { return props.children ? props.children(defaultValue) : null; },
      displayName: name,
      _currentValue: defaultValue,
      _currentValue2: defaultValue,
      _threadCount: 0,
      _defaultValue: defaultValue
    };
  };

  // Create fixed contexts
  window.MotionConfigContext = createFixedContext('MotionConfigContext', {
    transformPagePoint: undefined,
    isStatic: false,
    reducedMotion: "never"
  });
  
  window.LayoutGroupContext = createFixedContext('LayoutGroupContext', {});

  // STEP 4: Create mock modules for direct imports
  window.__framer_motion_LayoutGroupContext_mjs__ = {
    LayoutGroupContext: window.LayoutGroupContext,
    default: window.LayoutGroupContext
  };

  window.__framer_motion_MotionConfigContext_mjs__ = {
    MotionConfigContext: window.MotionConfigContext,
    default: window.MotionConfigContext
  };

  // STEP 5: Override fetch to intercept framer-motion module requests
  const originalFetch = window.fetch;
  window.fetch = function(url, options) {
    if (typeof url === 'string') {
      if (url.includes('MotionConfigContext.mjs')) {
        console.log('[DirectPatch] Intercepted fetch for MotionConfigContext.mjs');
        return Promise.resolve(new Response(
          `export const MotionConfigContext = window.MotionConfigContext; export default window.MotionConfigContext;`,
          { headers: { 'content-type': 'application/javascript' } }
        ));
      }
      
      if (url.includes('LayoutGroupContext.mjs')) {
        console.log('[DirectPatch] Intercepted fetch for LayoutGroupContext.mjs');
        return Promise.resolve(new Response(
          `export const LayoutGroupContext = window.LayoutGroupContext; export default window.LayoutGroupContext;`,
          { headers: { 'content-type': 'application/javascript' } }
        ));
      }
    }
    
    return originalFetch.apply(this, arguments);
  };

  // STEP 6: Override import.meta
  if (typeof window.import === 'undefined') {
    window.import = function(moduleId) {
      if (moduleId.includes('LayoutGroupContext')) {
        console.log('[DirectPatch] Intercepted import for LayoutGroupContext:', moduleId);
        return Promise.resolve(window.__framer_motion_LayoutGroupContext_mjs__);
      }
      
      if (moduleId.includes('MotionConfigContext')) {
        console.log('[DirectPatch] Intercepted import for MotionConfigContext:', moduleId);
        return Promise.resolve(window.__framer_motion_MotionConfigContext_mjs__);
      }
      
      return Promise.reject(new Error(`[DirectPatch] Cannot import module: ${moduleId}`));
    };
  }

  // STEP 7: Override dynamic imports
  if (typeof window.__vite__import === 'undefined') {
    window.__vite__import = function(moduleId) {
      if (moduleId.includes('LayoutGroupContext')) {
        console.log('[DirectPatch] Intercepted __vite__import for LayoutGroupContext:', moduleId);
        return Promise.resolve(window.__framer_motion_LayoutGroupContext_mjs__);
      }
      
      if (moduleId.includes('MotionConfigContext')) {
        console.log('[DirectPatch] Intercepted __vite__import for MotionConfigContext:', moduleId);
        return Promise.resolve(window.__framer_motion_MotionConfigContext_mjs__);
      }
      
      return Promise.reject(new Error(`[DirectPatch] Cannot import module: ${moduleId}`));
    };
  }

  // STEP 8: Monkey patch document.createElement to intercept script loading
  const originalCreateElement = document.createElement;
  document.createElement = function(tagName) {
    const element = originalCreateElement.apply(document, arguments);
    
    if (tagName.toLowerCase() === 'script') {
      const originalSetAttribute = element.setAttribute;
      element.setAttribute = function(name, value) {
        if (name === 'src' && typeof value === 'string') {
          if (value.includes('MotionConfigContext.mjs') || value.includes('LayoutGroupContext.mjs')) {
            console.log('[DirectPatch] Intercepted script src:', value);
            // Don't set the src attribute, effectively blocking the script
            return;
          }
        }
        return originalSetAttribute.apply(this, arguments);
      };
    }
    
    return element;
  };

  // STEP 9: Monkey patch document.head.appendChild to intercept script loading
  const originalAppendChild = document.head.appendChild;
  document.head.appendChild = function(element) {
    if (element.tagName && element.tagName.toLowerCase() === 'script' && element.src) {
      if (element.src.includes('MotionConfigContext.mjs') || element.src.includes('LayoutGroupContext.mjs')) {
        console.log('[DirectPatch] Blocked script append:', element.src);
        // Return a dummy element instead of appending
        return document.createComment('Blocked by DirectPatch');
      }
    }
    return originalAppendChild.apply(this, arguments);
  };

  console.log('[DirectPatch] Direct patch applied successfully');
})();
