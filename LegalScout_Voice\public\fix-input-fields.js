/**
 * Fix Interactive Elements
 *
 * This script fixes issues with input fields, dropdowns, and other interactive elements
 * that prevent users from clicking or interacting with them.
 */

(function() {
  // Function to fix interactive elements
  function fixInteractiveElements() {
    // Find all input fields
    const inputs = document.querySelectorAll('input, textarea, select, button, a[role="button"], [role="combobox"], [role="listbox"], [role="option"]');

    // Apply fixes to each input field
    inputs.forEach(input => {
      // Ensure the input is clickable
      input.style.pointerEvents = 'auto';
      input.style.position = 'relative';
      input.style.zIndex = '50';

      // Add event listeners to ensure focus works
      input.addEventListener('mousedown', function(e) {
        // Prevent any parent elements from capturing the event
        e.stopPropagation();
      });

      input.addEventListener('click', function(e) {
        // Ensure the input gets focus
        if (this.tagName === 'INPUT' || this.tagName === 'TEXTAREA' || this.tagName === 'SELECT') {
          this.focus();
        }
        e.stopPropagation();
      });
    });

    // Find all dropdown elements
    const dropdowns = document.querySelectorAll('.dropdown, .select, .dropdown-menu, .select-menu, .dropdown-options, .select-options, .modern-select, .modern-dropdown');

    // Apply fixes to each dropdown
    dropdowns.forEach(dropdown => {
      dropdown.style.pointerEvents = 'auto';
      dropdown.style.position = dropdown.classList.contains('dropdown-menu') ? 'absolute' : 'relative';
      dropdown.style.zIndex = dropdown.classList.contains('dropdown-menu') ? '100' : '60';

      // Make sure dropdown menus are visible when they should be
      if (dropdown.classList.contains('dropdown-menu') ||
          dropdown.classList.contains('select-menu') ||
          dropdown.classList.contains('dropdown-options') ||
          dropdown.classList.contains('select-options')) {
        // Only set display if it's supposed to be visible
        if (dropdown.getAttribute('aria-expanded') === 'true' ||
            dropdown.style.display === 'block' ||
            dropdown.classList.contains('show') ||
            dropdown.classList.contains('open')) {
          dropdown.style.display = 'block';
        }
      }
    });

    // Fix glass-effect containers
    const glassEffects = document.querySelectorAll('.glass-effect, .start-option');
    glassEffects.forEach(element => {
      element.style.pointerEvents = 'auto';
      element.style.position = 'relative';
      element.style.zIndex = '40';
    });

    // Fix url-input-group containers
    const urlInputGroups = document.querySelectorAll('.url-input-group');
    urlInputGroups.forEach(element => {
      element.style.pointerEvents = 'auto';
      element.style.position = 'relative';
      element.style.zIndex = '45';
    });

    // Fix input-group containers
    const inputGroups = document.querySelectorAll('.input-group');
    inputGroups.forEach(element => {
      element.style.pointerEvents = 'auto';
      element.style.position = 'relative';
      element.style.zIndex = '45';
    });

    // Specifically target the firmUrl input
    const firmUrlInput = document.getElementById('firmUrl');
    if (firmUrlInput) {
      firmUrlInput.style.pointerEvents = 'auto';
      firmUrlInput.style.position = 'relative';
      firmUrlInput.style.zIndex = '50';
      firmUrlInput.style.cursor = 'text';

      // Add a more aggressive click handler
      firmUrlInput.addEventListener('mousedown', function(e) {
        // Prevent any parent elements from capturing the event
        e.stopPropagation();
        e.preventDefault();

        // Force focus
        setTimeout(() => {
          this.focus();
        }, 0);
      });
    }

    // Find any potential overlay elements that might be blocking interaction
    const overlays = document.querySelectorAll('.overlay, .modal-overlay, .backdrop, .background-layer, .animated-background');
    overlays.forEach(overlay => {
      // Check if this overlay might be blocking interaction
      const computedStyle = window.getComputedStyle(overlay);
      const position = computedStyle.getPropertyValue('position');
      const zIndex = parseInt(computedStyle.getPropertyValue('z-index'), 10);

      // If it's a positioned element with a high z-index, adjust it
      if ((position === 'absolute' || position === 'fixed') && zIndex > 30) {
        overlay.style.pointerEvents = 'none';
      }
    });

    // Fix any elements with backdrop-filter that might be causing issues
    const backdropElements = document.querySelectorAll('[style*="backdrop-filter"]');
    backdropElements.forEach(element => {
      // Make sure backdrop-filter elements don't block interaction
      element.style.pointerEvents = 'auto';
    });
  }

  // Run the fix when the DOM is loaded
  document.addEventListener('DOMContentLoaded', fixInteractiveElements);

  // Also run the fix periodically to catch dynamically added elements
  setInterval(fixInteractiveElements, 1000);

  // Add a global click handler to help with debugging
  document.addEventListener('click', function(e) {
    console.log('Click event on:', e.target);

    // Check if we're clicking near an input or dropdown
    const inputs = document.querySelectorAll('input, select, .dropdown, .select');
    inputs.forEach(input => {
      const rect = input.getBoundingClientRect();
      const isNearby = (
        e.clientX >= rect.left - 20 &&
        e.clientX <= rect.right + 20 &&
        e.clientY >= rect.top - 20 &&
        e.clientY <= rect.bottom + 20
      );

      if (isNearby) {
        console.log('Nearby interactive element:', input);

        // If it's an input, try to focus it
        if (input.tagName === 'INPUT' || input.tagName === 'SELECT') {
          setTimeout(() => {
            input.focus();
            console.log('Forced focus on:', input);
          }, 0);
        }
      }
    });
  });
})();
