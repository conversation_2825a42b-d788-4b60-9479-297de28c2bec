console.log('Starting basic server...');

const http = require('http');
const fs = require('fs');
const path = require('path');

const port = 3000;

console.log('Creating server...');

const server = http.createServer((req, res) => {
  console.log('Request received:', req.method, req.url);

  // Handle CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  // Determine file path
  let filePath;
  if (req.url === '/' || req.url === '/index.html') {
    filePath = path.join(__dirname, 'dist', 'index.html');
  } else {
    filePath = path.join(__dirname, 'dist', req.url);
  }

  // Check if file exists
  fs.access(filePath, fs.constants.F_OK, (err) => {
    if (err) {
      // If file doesn't exist, serve index.html for SPA routing
      filePath = path.join(__dirname, 'dist', 'index.html');
    }

    // Determine content type
    const ext = path.extname(filePath).toLowerCase();
    let contentType = 'text/html';

    switch (ext) {
      case '.js':
      case '.mjs':
        contentType = 'text/javascript';
        break;
      case '.css':
        contentType = 'text/css';
        break;
      case '.json':
        contentType = 'application/json';
        break;
      case '.png':
        contentType = 'image/png';
        break;
      case '.jpg':
      case '.jpeg':
        contentType = 'image/jpeg';
        break;
      case '.gif':
        contentType = 'image/gif';
        break;
      case '.svg':
        contentType = 'image/svg+xml';
        break;
      case '.ico':
        contentType = 'image/x-icon';
        break;
    }

    // Read and serve file
    fs.readFile(filePath, (error, content) => {
      if (error) {
        console.error('Error reading file:', error);
        res.writeHead(500);
        res.end('Server error: ' + error.message);
      } else {
        res.writeHead(200, { 'Content-Type': contentType });
        res.end(content);
        console.log('Served:', req.url, '->', filePath);
      }
    });
  });
});

console.log('Starting server on port', port);

server.listen(port, (err) => {
  if (err) {
    console.error('Server failed to start:', err);
  } else {
    console.log(`🚀 Basic server running at http://localhost:${port}`);
    console.log('Server is ready!');
  }
});

server.on('error', (err) => {
  console.error('Server error:', err);
});

console.log('Server setup complete');
