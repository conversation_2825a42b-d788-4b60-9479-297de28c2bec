<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Test</title>
    
    <!-- Load Three.js from cdnjs -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    
    <!-- Load Supabase from CDN -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        #status {
            margin-bottom: 20px;
            padding: 10px;
            background-color: #f0f0f0;
            border-radius: 5px;
        }
        button {
            padding: 10px;
            margin: 5px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
    </style>
</head>
<body>
    <h1>Simple Test</h1>
    
    <div id="status">Loading...</div>
    
    <div>
        <button id="testThree">Test Three.js</button>
        <button id="testSupabase">Test Supabase</button>
        <button id="createAttorney">Create Attorney</button>
    </div>
    
    <div id="result" style="margin-top: 20px;"></div>
    
    <script>
        // Status reporting
        function updateStatus(message) {
            document.getElementById('status').innerHTML += '<br>' + message;
            console.log(message);
        }
        
        // Generate a UUID v4
        function generateUUID() {
            return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
                var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
                return v.toString(16);
            });
        }
        
        // Create a default attorney
        function createDefaultAttorney() {
            return {
                id: generateUUID(),
                subdomain: 'default',
                firm_name: 'Your Law Firm',
                name: 'Your Name',
                is_active: true,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString(),
                voice_provider: '11labs',
                voice_id: 'sarah',
                welcome_message: 'Hello! I\'m your legal assistant. How can I help you today?',
                information_gathering_prompt: 'Tell me about your situation, and I\'ll help find the right solution for you.',
                vapi_instructions: 'You are a legal assistant helping potential clients understand their legal needs and connecting them with appropriate attorneys.',
                vapi_assistant_id: '8d962209-530e-45d2-b2d6-17ed1ef55b3c' // Use the known working assistant ID
            };
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus('DOM loaded');
            
            // Check if THREE is defined
            if (typeof THREE !== 'undefined') {
                updateStatus('THREE is defined');
            } else {
                updateStatus('THREE is NOT defined');
            }
            
            // Check if supabase is defined
            if (typeof supabase !== 'undefined') {
                updateStatus('supabase is defined');
            } else {
                updateStatus('supabase is NOT defined');
            }
            
            // Test Three.js button
            document.getElementById('testThree').addEventListener('click', function() {
                try {
                    updateStatus('Testing Three.js...');
                    
                    // Create a scene
                    const scene = new THREE.Scene();
                    updateStatus('Scene created successfully');
                    
                    // Create a camera
                    const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
                    camera.position.z = 5;
                    updateStatus('Camera created successfully');
                    
                    // Create a renderer
                    const renderer = new THREE.WebGLRenderer();
                    renderer.setSize(400, 300);
                    document.getElementById('result').innerHTML = '';
                    document.getElementById('result').appendChild(renderer.domElement);
                    updateStatus('Renderer created successfully');
                    
                    // Create a cube
                    const geometry = new THREE.BoxGeometry();
                    const material = new THREE.MeshBasicMaterial({ color: 0x00ff00 });
                    const cube = new THREE.Mesh(geometry, material);
                    scene.add(cube);
                    updateStatus('Cube created and added to scene');
                    
                    // Animation function
                    function animate() {
                        requestAnimationFrame(animate);
                        
                        cube.rotation.x += 0.01;
                        cube.rotation.y += 0.01;
                        
                        renderer.render(scene, camera);
                    }
                    
                    // Start animation
                    animate();
                    updateStatus('Animation started');
                } catch (error) {
                    updateStatus('Error: ' + error.message);
                }
            });
            
            // Test Supabase button
            document.getElementById('testSupabase').addEventListener('click', function() {
                try {
                    updateStatus('Testing Supabase...');
                    
                    // Initialize Supabase client
                    const supabaseUrl = 'https://utopqxsvudgrtiwenlzl.supabase.co';
                    const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzg5NDgwMDcsImV4cCI6MjA1NDUyNDAwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU';
                    
                    // Create Supabase client
                    const supabaseClient = supabase.createClient(supabaseUrl, supabaseKey);
                    updateStatus('Supabase client created');
                    
                    // Test a simple query
                    supabaseClient
                        .from('attorneys')
                        .select('id, name, firm_name')
                        .limit(5)
                        .then(({ data, error }) => {
                            if (error) {
                                updateStatus('Error: ' + error.message);
                            } else {
                                updateStatus('Query successful! Found ' + data.length + ' attorneys');
                                document.getElementById('result').innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
                            }
                        })
                        .catch(error => {
                            updateStatus('Unexpected error: ' + error.message);
                        });
                } catch (error) {
                    updateStatus('Error: ' + error.message);
                }
            });
            
            // Create Attorney button
            document.getElementById('createAttorney').addEventListener('click', function() {
                try {
                    updateStatus('Creating attorney...');
                    
                    // Create a default attorney
                    const attorney = createDefaultAttorney();
                    
                    // Save to localStorage
                    localStorage.setItem('attorney', JSON.stringify(attorney));
                    localStorage.setItem('attorney_id', attorney.id);
                    localStorage.setItem('attorney_version', Date.now().toString());
                    
                    updateStatus('Attorney created and saved to localStorage: ' + attorney.id);
                    document.getElementById('result').innerHTML = '<pre>' + JSON.stringify(attorney, null, 2) + '</pre>';
                } catch (error) {
                    updateStatus('Error: ' + error.message);
                }
            });
        });
    </script>
</body>
</html>
