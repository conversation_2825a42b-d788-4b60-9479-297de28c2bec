/**
 * Fix VAPI Assistant Switch
 *
 * This script fixes the issue where selecting a different attorney from the dropdown
 * doesn't update the VAPI assistant ID in the Agent Configuration tab.
 *
 * It also adds functionality to load assistant data directly from VAPI.
 */

(function() {
  console.log('[FixVapiAssistantSwitch] Starting fix...');

  // Wait for the StandaloneAttorneyManager to be available
  const waitForManager = () => {
    if (window.standaloneAttorneyManager) {
      applyFix();
    } else {
      console.log('[FixVapiAssistantSwitch] Waiting for StandaloneAttorneyManager...');
      setTimeout(waitForManager, 100);
    }
  };

  // Function to load assistant data from VAPI
  const loadAssistantFromVapi = async (assistantId) => {
    if (!assistantId) {
      console.error('[FixVapiAssistantSwitch] No assistant ID provided');
      return null;
    }

    try {
      console.log(`[FixVapiAssistantSwitch] Loading assistant ${assistantId} from VAPI`);

      // Use MCP to get assistant details
      if (window.mcp) {
        const assistant = await window.mcp.invoke('get_assistant_vapi-mcp-server', {
          assistantId: assistantId
        });

        if (assistant) {
          console.log('[FixVapiAssistantSwitch] Loaded assistant from VAPI:', assistant);
          return assistant;
        }
      }

      return null;
    } catch (error) {
      console.error('[FixVapiAssistantSwitch] Error loading assistant from VAPI:', error);
      return null;
    }
  };

  // Function to update attorney with data from VAPI
  const updateAttorneyWithVapiData = async (attorneyId, assistantId) => {
    try {
      console.log(`[FixVapiAssistantSwitch] Updating attorney ${attorneyId} with data from VAPI assistant ${assistantId}`);

      // Load the assistant data from VAPI
      const assistant = await loadAssistantFromVapi(assistantId);

      if (!assistant) {
        console.error('[FixVapiAssistantSwitch] Failed to load assistant from VAPI');
        return false;
      }

      // Get the attorney from localStorage
      const storedAttorney = localStorage.getItem('attorney');
      if (!storedAttorney) {
        console.error('[FixVapiAssistantSwitch] No attorney found in localStorage');
        return false;
      }

      const parsedAttorney = JSON.parse(storedAttorney);

      // Update the attorney with data from VAPI
      const updatedAttorney = {
        ...parsedAttorney,
        welcome_message: assistant.firstMessage || parsedAttorney.welcome_message,
        vapi_instructions: assistant.instructions || parsedAttorney.vapi_instructions,
        voice_id: assistant.voice?.voiceId || parsedAttorney.voice_id,
        voice_provider: assistant.voice?.provider || parsedAttorney.voice_provider
      };

      // Save to localStorage
      localStorage.setItem('attorney', JSON.stringify(updatedAttorney));

      // Update the attorney in the StandaloneAttorneyManager
      if (window.standaloneAttorneyManager) {
        window.standaloneAttorneyManager.attorney = updatedAttorney;
        window.standaloneAttorneyManager.notifySubscribers();
      }

      console.log('[FixVapiAssistantSwitch] Attorney updated with data from VAPI');
      return true;
    } catch (error) {
      console.error('[FixVapiAssistantSwitch] Error updating attorney with data from VAPI:', error);
      return false;
    }
  };

  // Apply the fix to the StandaloneAttorneyManager
  const applyFix = () => {
    try {
      console.log('[FixVapiAssistantSwitch] Applying fix to StandaloneAttorneyManager...');

      // Store the original switchAttorney method
      const originalSwitchAttorney = window.standaloneAttorneyManager.switchAttorney;

      // Replace with our enhanced version
      window.standaloneAttorneyManager.switchAttorney = async function(attorneyId) {
        console.log(`[FixVapiAssistantSwitch] Enhanced switchAttorney called with attorneyId: ${attorneyId}`);

        // Call the original method
        const result = originalSwitchAttorney.call(this, attorneyId);

        if (result) {
          console.log('[FixVapiAssistantSwitch] Attorney switched successfully, updating VAPI assistant info');

          // Dispatch a custom event to notify that the VAPI assistant ID has changed
          const event = new CustomEvent('vapiAssistantChanged', {
            detail: {
              attorneyId: attorneyId,
              vapiAssistantId: this.attorney.vapi_assistant_id
            }
          });
          window.dispatchEvent(event);

          // Force a refresh of the preview config
          if (window.updatePreviewConfig) {
            window.updatePreviewConfig({
              vapiAssistantId: this.attorney.vapi_assistant_id
            });
          }

          // Force a refresh of the agent tab
          if (window.refreshAgentTab) {
            window.refreshAgentTab();
          }

          // Update the attorney with data from VAPI
          if (this.attorney.vapi_assistant_id) {
            updateAttorneyWithVapiData(attorneyId, this.attorney.vapi_assistant_id);
          }
        }

        return result;
      };

      // Add a listener for the vapiAssistantChanged event
      window.addEventListener('vapiAssistantChanged', (event) => {
        console.log('[FixVapiAssistantSwitch] VAPI assistant changed:', event.detail);

        // Force a refresh of the preview config
        if (window.updatePreviewConfig) {
          window.updatePreviewConfig({
            vapiAssistantId: event.detail.vapiAssistantId
          });
        }
      });

      // Add a global function to update the preview config
      window.updatePreviewConfig = (config) => {
        console.log('[FixVapiAssistantSwitch] Updating preview config:', config);

        // Dispatch a custom event to update the preview config
        const event = new CustomEvent('updatePreviewConfig', {
          detail: config
        });
        window.dispatchEvent(event);
      };

      // Add a global function to refresh the agent tab
      window.refreshAgentTab = () => {
        console.log('[FixVapiAssistantSwitch] Refreshing agent tab');

        // Dispatch a custom event to refresh the agent tab
        const event = new CustomEvent('refreshAgentTab');
        window.dispatchEvent(event);
      };

      // Add a global function to load data from VAPI
      window.loadFromVapi = async (assistantId) => {
        console.log('[FixVapiAssistantSwitch] Loading data from VAPI for assistant:', assistantId);

        // Load the assistant data from VAPI
        const assistant = await loadAssistantFromVapi(assistantId);

        if (!assistant) {
          console.error('[FixVapiAssistantSwitch] Failed to load assistant from VAPI');
          return null;
        }

        // Extract the data we need from the VAPI assistant
        const vapiData = {
          welcomeMessage: assistant.firstMessage || '',
          vapiInstructions: assistant.instructions || '',
          voiceId: assistant.voice?.voiceId || 'sarah',
          voiceProvider: assistant.voice?.provider || '11labs'
        };

        // Dispatch a custom event with the VAPI data
        const event = new CustomEvent('vapiDataLoaded', {
          detail: vapiData
        });
        window.dispatchEvent(event);

        return vapiData;
      };

      console.log('[FixVapiAssistantSwitch] Fix applied successfully');
    } catch (error) {
      console.error('[FixVapiAssistantSwitch] Error applying fix:', error);
    }
  };

  // Start the fix
  waitForManager();
})();
