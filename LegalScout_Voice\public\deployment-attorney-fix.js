/**
 * Deployment Attorney Fix
 * 
 * This script provides fixes for attorney profile management in deployment environments
 * where Vapi MCP services might not be available.
 */

(function() {
  console.log('[DeploymentAttorneyFix] Initializing...');
  
  // Storage keys
  const STORAGE_KEYS = {
    ATTORNEY: 'attorney',
    ATTORNEY_ID: 'attorney_id',
    ATTORNEY_VERSION: 'attorney_version'
  };
  
  // Default attorney template
  const DEFAULT_ATTORNEY = {
    id: '',
    name: 'Attorney',
    email: '',
    firm_name: 'Law Firm',
    welcome_message: 'Hello, I\'m your legal assistant. How can I help you today?',
    vapi_instructions: 'You are a legal assistant. Help potential clients understand their legal needs and collect relevant information for consultation.',
    voice_provider: '11labs',
    voice_id: 'sarah',
    vapi_assistant_id: '',
    created_at: '',
    updated_at: ''
  };
  
  /**
   * Generate a UUID v4
   * @returns {string} A UUID v4 string
   */
  function generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }
  
  /**
   * Validate if a string is a valid UUID
   * @param {string} id - The ID to validate
   * @returns {boolean} Whether the ID is a valid UUID
   */
  function isValidUUID(id) {
    if (!id) return false;
    
    // Check if it's a development ID
    if (typeof id === 'string' && id.startsWith && id.startsWith('dev-')) {
      return true;
    }
    
    // Regular UUID validation
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(id);
  }
  
  /**
   * Create a default attorney with optional overrides
   * @param {Object} overrides - Optional property overrides
   * @returns {Object} A default attorney object
   */
  function createDefaultAttorney(overrides = {}) {
    const now = new Date().toISOString();
    
    return {
      ...DEFAULT_ATTORNEY,
      id: generateUUID(),
      created_at: now,
      updated_at: now,
      ...overrides
    };
  }
  
  /**
   * Safely check if a value starts with a prefix
   * @param {any} value - The value to check
   * @param {string} prefix - The prefix to check for
   * @returns {boolean} Whether the value starts with the prefix
   */
  function safeStartsWith(value, prefix) {
    return typeof value === 'string' && 
           typeof value.startsWith === 'function' && 
           value.startsWith(prefix);
  }
  
  /**
   * Patch the AttorneyProfileManager._initialize method to handle undefined values
   */
  function patchAttorneyProfileManagerInitialize() {
    // Wait for the AttorneyProfileManager to be defined
    const checkInterval = setInterval(() => {
      try {
        // Find the AttorneyProfileManager class
        const AttorneyProfileManager = window.attorneyProfileManager && 
                                      window.attorneyProfileManager.constructor;
        
        if (AttorneyProfileManager && AttorneyProfileManager.prototype) {
          clearInterval(checkInterval);
          
          console.log('[DeploymentAttorneyFix] Found AttorneyProfileManager, patching _initialize method');
          
          // Save the original _initialize method
          const originalInitialize = AttorneyProfileManager.prototype._initialize;
          
          // Replace with our safer version
          AttorneyProfileManager.prototype._initialize = async function(userId, email = null) {
            try {
              console.log('[DeploymentAttorneyFix] Safe _initialize called with userId:', userId, 'email:', email);
              
              if (this.isInitialized && this.currentAttorney) {
                console.log('[DeploymentAttorneyFix] Already initialized with attorney:', this.currentAttorney.id);
                return this.currentAttorney;
              }
              
              // Try multiple methods to find the attorney profile
              let attorney = null;
              
              // Method 1: Try to load by user ID
              if (userId) {
                try {
                  attorney = await this.loadAttorneyByUserId(userId);
                  if (attorney) {
                    console.log('[DeploymentAttorneyFix] Found attorney by userId:', attorney.id);
                  }
                } catch (error) {
                  console.warn('[DeploymentAttorneyFix] Error loading by userId:', error);
                }
              }
              
              // Method 2: Try to load by email if available
              if (!attorney && email) {
                try {
                  attorney = await this.loadAttorneyByEmail(email);
                  if (attorney) {
                    console.log('[DeploymentAttorneyFix] Found attorney by email:', attorney.id);
                    
                    // If found by email but not linked to user ID, update the link
                    if (userId && (!attorney.user_id || attorney.user_id !== userId)) {
                      console.log('[DeploymentAttorneyFix] Linking attorney to userId:', userId);
                      attorney = await this.updateAttorneyInSupabase({
                        id: attorney.id,
                        user_id: userId
                      });
                    }
                  }
                } catch (error) {
                  console.warn('[DeploymentAttorneyFix] Error loading by email:', error);
                }
              }
              
              // Method 3: Try to load from localStorage
              if (!attorney) {
                try {
                  attorney = this.loadFromLocalStorage();
                  if (attorney) {
                    console.log('[DeploymentAttorneyFix] Found attorney in localStorage:', attorney.id);
                  }
                } catch (error) {
                  console.warn('[DeploymentAttorneyFix] Error loading from localStorage:', error);
                }
              }
              
              // Method 4: Create a new attorney if none found
              if (!attorney) {
                // In development mode, create a development attorney
                const isDevelopment = typeof window !== 'undefined' && 
                                     (window.location.hostname === 'localhost' || 
                                      window.location.hostname === '127.0.0.1');
                
                if (isDevelopment) {
                  attorney = this.createDefaultAttorney({
                    id: 'dev-attorney-' + Date.now(),
                    name: 'Development Attorney',
                    email: email || '<EMAIL>',
                    user_id: userId
                  });
                  
                  console.log('[DeploymentAttorneyFix] Created development attorney:', attorney.id);
                } else {
                  // In production, create a regular attorney
                  attorney = this.createDefaultAttorney({
                    user_id: userId,
                    email: email
                  });
                  
                  // Try to save to Supabase
                  try {
                    attorney = await this.createAttorneyInSupabase(attorney);
                    console.log('[DeploymentAttorneyFix] Created attorney in Supabase:', attorney.id);
                  } catch (error) {
                    console.warn('[DeploymentAttorneyFix] Error creating attorney in Supabase:', error);
                  }
                }
              }
              
              if (attorney) {
                // Store the attorney data
                this.currentAttorney = attorney;
                
                // Save to localStorage for offline access
                this.saveToLocalStorage(attorney);
                
                // Set up Supabase Realtime subscription for non-development attorneys
                if (!safeStartsWith(attorney.id, 'dev-')) {
                  this.setupRealtimeSubscription(attorney.id);
                } else {
                  console.log('[DeploymentAttorneyFix] Development attorney, skipping Realtime subscription');
                }
                
                // Check Vapi synchronization - with error handling
                try {
                  await this.checkVapiSynchronization(attorney);
                } catch (error) {
                  console.warn('[DeploymentAttorneyFix] Error checking Vapi synchronization:', error);
                  
                  // Set a default sync status
                  this.syncStatus = {
                    consistent: true,
                    message: 'Vapi service not available in deployment environment',
                    lastChecked: new Date(),
                    warning: error.message
                  };
                  
                  this.lastSyncTime = new Date();
                }
                
                this.isInitialized = true;
                this.notifyListeners();
                
                return attorney;
              }
              
              throw new Error('Failed to initialize attorney profile');
            } catch (error) {
              console.error('[DeploymentAttorneyFix] Initialization error:', error);
              
              // Create a fallback attorney
              const fallbackAttorney = this.createDefaultAttorney({
                id: 'fallback-' + Date.now(),
                name: 'Attorney',
                email: email || '',
                user_id: userId
              });
              
              // Store the fallback attorney
              this.currentAttorney = fallbackAttorney;
              this.saveToLocalStorage(fallbackAttorney);
              this.isInitialized = true;
              this.notifyListeners();
              
              return fallbackAttorney;
            }
          };
          
          console.log('[DeploymentAttorneyFix] Successfully patched AttorneyProfileManager._initialize');
        }
      } catch (error) {
        console.error('[DeploymentAttorneyFix] Error patching AttorneyProfileManager:', error);
      }
    }, 500);
    
    // Clear interval after 30 seconds
    setTimeout(() => {
      clearInterval(checkInterval);
      console.warn('[DeploymentAttorneyFix] Timed out waiting for AttorneyProfileManager');
    }, 30000);
  }
  
  /**
   * Patch the VapiMcpService to handle connection errors gracefully
   */
  function patchVapiMcpService() {
    // Wait for the VapiMcpService to be defined
    const checkInterval = setInterval(() => {
      try {
        // Find the VapiMcpService instance
        const vapiMcpService = window.vapiMcpService || 
                              (window.vapiServiceManager && window.vapiServiceManager.getMcpService());
        
        if (vapiMcpService) {
          clearInterval(checkInterval);
          
          console.log('[DeploymentAttorneyFix] Found VapiMcpService, patching connect method');
          
          // Save the original connect method
          const originalConnect = vapiMcpService.connect;
          
          // Replace with our safer version
          vapiMcpService.connect = async function(apiKey) {
            try {
              // Try the original connect method
              return await originalConnect.call(this, apiKey);
            } catch (error) {
              console.warn('[DeploymentAttorneyFix] Error connecting to Vapi, using mock mode:', error);
              
              // Set up mock mode
              this.useMock = true;
              this.connected = true;
              
              return true;
            }
          };
          
          // Patch the createAssistant method
          const originalCreateAssistant = vapiMcpService.createAssistant;
          
          vapiMcpService.createAssistant = async function(assistantConfig) {
            try {
              // Try the original method
              return await originalCreateAssistant.call(this, assistantConfig);
            } catch (error) {
              console.warn('[DeploymentAttorneyFix] Error creating Vapi assistant, using mock:', error);
              
              // Return a mock assistant
              return {
                id: 'mock-assistant-' + Date.now(),
                name: assistantConfig.name,
                instructions: assistantConfig.instructions,
                firstMessage: assistantConfig.firstMessage,
                voice: assistantConfig.voice,
                mock: true
              };
            }
          };
          
          // Patch the updateAssistant method
          const originalUpdateAssistant = vapiMcpService.updateAssistant;
          
          vapiMcpService.updateAssistant = async function(assistantId, assistantConfig) {
            try {
              // Try the original method
              return await originalUpdateAssistant.call(this, assistantId, assistantConfig);
            } catch (error) {
              console.warn('[DeploymentAttorneyFix] Error updating Vapi assistant, using mock:', error);
              
              // Return a mock updated assistant
              return {
                id: assistantId,
                name: assistantConfig.name,
                instructions: assistantConfig.instructions,
                firstMessage: assistantConfig.firstMessage,
                voice: assistantConfig.voice,
                mock: true,
                updated: true
              };
            }
          };
          
          console.log('[DeploymentAttorneyFix] Successfully patched VapiMcpService');
        }
      } catch (error) {
        console.error('[DeploymentAttorneyFix] Error patching VapiMcpService:', error);
      }
    }, 500);
    
    // Clear interval after 30 seconds
    setTimeout(() => {
      clearInterval(checkInterval);
      console.warn('[DeploymentAttorneyFix] Timed out waiting for VapiMcpService');
    }, 30000);
  }
  
  // Apply the patches
  patchAttorneyProfileManagerInitialize();
  patchVapiMcpService();
  
  console.log('[DeploymentAttorneyFix] Initialization complete');
})();
