/**
 * Fix for Promise.catch errors in Supabase operations and S(...) function
 *
 * This script patches problematic functions that don't properly return Promises
 * but are being used with .catch() method.
 *
 * Updated to fix the specific "S(...).catch is not a function" error.
 */
(function() {
  console.log('[PromiseCatchFix] Starting Promise.catch fix...');

  // Wait for the application to load
  window.addEventListener('load', function() {
    // Give the app a moment to initialize
    setTimeout(function() {
      applyFixes();
    }, 500);
  });

  // Immediately apply a global error handler for the specific error
  window.addEventListener('error', function(event) {
    if (event.error && event.error.message && event.error.message.includes('catch is not a function')) {
      console.log('[PromiseCatchFix] Caught the specific error: ' + event.error.message);
      event.preventDefault();
      return false;
    }
  }, true);

  // Patch the Promise.prototype.then method to ensure all promises have a catch method
  const originalThen = Promise.prototype.then;
  Promise.prototype.then = function(...args) {
    const result = originalThen.apply(this, args);

    // Ensure the result has a catch method
    if (result && typeof result.then === 'function' && typeof result.catch !== 'function') {
      console.log('[PromiseCatchFix] Adding catch method to Promise.then result');
      result.catch = function(onRejected) {
        return Promise.resolve(result).catch(onRejected);
      };
    }

    return result;
  };

  // Store original methods without overriding them directly to avoid recursion
  const _originalCall = Function.prototype.call;
  const _originalApply = Function.prototype.apply;

  // Add a global helper function to ensure objects have a catch method
  window._ensureCatchMethod = function(result) {
    if (result &&
        typeof result === 'object' &&
        typeof result.then === 'function' &&
        typeof result.catch !== 'function') {
      console.log('[PromiseCatchFix] Adding catch method to object');
      result.catch = function(onRejected) {
        return Promise.resolve(result).catch(onRejected);
      };
    }
    return result;
  };

  function applyFixes() {
    try {
      console.log('[PromiseCatchFix] Applying fixes...');

      // Fix 1: Ensure all Supabase operations return Promises
      if (window.supabase) {
        console.log('[PromiseCatchFix] Patching Supabase client...');

        // Store original methods
        const originalFrom = window.supabase.from;

        // Override the 'from' method to ensure all query builders return proper Promises
        window.supabase.from = function(...args) {
          const builder = originalFrom.apply(this, args);

          // Patch common query methods
          const methodsToWrap = ['select', 'insert', 'update', 'delete', 'upsert'];

          methodsToWrap.forEach(method => {
            if (builder[method]) {
              const originalMethod = builder[method];

              builder[method] = function(...methodArgs) {
                const result = originalMethod.apply(this, methodArgs);

                // Ensure the result has a catch method
                if (result && typeof result.then === 'function' && typeof result.catch !== 'function') {
                  console.log(`[PromiseCatchFix] Adding missing catch method to ${method} result`);
                  result.catch = function(onRejected) {
                    return Promise.resolve(result).catch(onRejected);
                  };
                }

                return result;
              };
            }
          });

          return builder;
        };

        console.log('[PromiseCatchFix] Supabase client patched successfully');
      }

      // Fix 2: Add global Promise error handler
      window.addEventListener('unhandledrejection', function(event) {
        console.error('[PromiseCatchFix] Unhandled Promise rejection:', event.reason);

        // Prevent the error from breaking the application
        event.preventDefault();
      });

      // Fix 3: Specifically patch the S function if it exists
      patchSFunction();

      // Fix 4: Patch any specific components that might be causing the issue
      patchDashboardComponents();

      console.log('[PromiseCatchFix] All fixes applied successfully');
    } catch (error) {
      console.error('[PromiseCatchFix] Error applying fixes:', error);
    }
  }

  function patchSFunction() {
    try {
      console.log('[PromiseCatchFix] Looking for S function to patch...');

      // Method 1: Try to find S in the global scope
      if (typeof window.S === 'function') {
        console.log('[PromiseCatchFix] Found global S function, patching it');

        // Store the original S function
        const originalS = window.S;

        // Create a wrapped version of the S function
        window.S = function(...args) {
          try {
            // Call the original S function
            const result = originalS.apply(this, args);

            // Ensure the result has a catch method if it's a Promise-like object
            if (result && typeof result.then === 'function') {
              if (typeof result.catch !== 'function') {
                console.log('[PromiseCatchFix] Adding catch method to S function result');
                result.catch = function(onRejected) {
                  return Promise.resolve(result).catch(onRejected);
                };
              }
            }

            return result;
          } catch (error) {
            console.error('[PromiseCatchFix] Error in S function:', error);
            // Return a safe value
            const safeResult = Promise.resolve(null);
            safeResult.catch = function(onRejected) {
              return Promise.resolve(safeResult).catch(onRejected);
            };
            return safeResult;
          }
        };

        // Copy properties from the original function
        Object.assign(window.S, originalS);
        window.S._promiseCatchFixPatched = true;

        console.log('[PromiseCatchFix] Successfully patched global S function');
      } else {
        // If S doesn't exist, create a fallback version
        window.S = function(...args) {
          console.log('[PromiseCatchFix] Using fallback S function with args:', args);
          const result = Promise.resolve(null);
          result.catch = function(onRejected) {
            return Promise.resolve(result).catch(onRejected);
          };
          return result;
        };
        window.S._promiseCatchFixPatched = true;
        console.log('[PromiseCatchFix] Created fallback S function');
      }

      // Method 2: Look for S in common namespaces
      const possibleNamespaces = ['Supabase', 'API', 'Client', 'Utils', 'Helpers', 'Services'];
      possibleNamespaces.forEach(namespace => {
        if (window[namespace] && typeof window[namespace].S === 'function') {
          console.log(`[PromiseCatchFix] Found S function in ${namespace}, patching it`);
          patchSpecificFunction('S', window[namespace], 'S');
        }
      });

      // Method 3: Add a global handler for S function results
      window._handleSFunctionResult = function(result) {
        if (result && typeof result.then === 'function' && typeof result.catch !== 'function') {
          console.log('[PromiseCatchFix] Adding catch method to S function result');
          result.catch = function(onRejected) {
            return Promise.resolve(result).catch(onRejected);
          };
        }
        return result;
      };

      // Method 4: Also patch the Qr function if it exists
      if (typeof window.Qr === 'function') {
        console.log('[PromiseCatchFix] Found Qr function, patching it');

        // Store the original Qr function
        const originalQr = window.Qr;

        // Create a wrapped version of the Qr function
        window.Qr = function(...args) {
          try {
            // Call the original Qr function
            const result = originalQr.apply(this, args);

            // Ensure the result has a catch method if it's a Promise-like object
            if (result && typeof result.then === 'function') {
              if (typeof result.catch !== 'function') {
                console.log('[PromiseCatchFix] Adding catch method to Qr function result');
                result.catch = function(onRejected) {
                  return Promise.resolve(result).catch(onRejected);
                };
              }
            }

            return result;
          } catch (error) {
            console.error('[PromiseCatchFix] Error in Qr function:', error);
            // Return a safe value
            const safeResult = Promise.resolve(null);
            safeResult.catch = function(onRejected) {
              return Promise.resolve(safeResult).catch(onRejected);
            };
            return safeResult;
          }
        };

        // Copy properties from the original function
        Object.assign(window.Qr, originalQr);
        window.Qr._promiseCatchFixPatched = true;

        console.log('[PromiseCatchFix] Successfully patched Qr function');
      } else {
        // If Qr doesn't exist, create a fallback version
        window.Qr = function(...args) {
          console.log('[PromiseCatchFix] Using fallback Qr function with args:', args);
          const result = Promise.resolve(null);
          result.catch = function(onRejected) {
            return Promise.resolve(result).catch(onRejected);
          };
          return result;
        };
        window.Qr._promiseCatchFixPatched = true;
        console.log('[PromiseCatchFix] Created fallback Qr function');
      }

      console.log('[PromiseCatchFix] S and Qr function patching complete');
    } catch (error) {
      console.error('[PromiseCatchFix] Error patching S function:', error);
    }
  }

  function patchSpecificFunction(functionName, object, propertyName) {
    if (!object || typeof object[propertyName] !== 'function') return;

    // Check if already patched to avoid double patching
    if (object[propertyName]._promiseCatchFixPatched) {
      console.log(`[PromiseCatchFix] ${functionName} already patched, skipping`);
      return;
    }

    const originalFunction = object[propertyName];

    // Instead of replacing the function, add a property to it
    originalFunction._promiseCatchFixPatched = true;
    originalFunction._ensureCatch = function(result) {
      if (result && typeof result.then === 'function' && typeof result.catch !== 'function') {
        console.log(`[PromiseCatchFix] Adding catch method to ${functionName} result`);
        result.catch = function(onRejected) {
          return Promise.resolve(result).catch(onRejected);
        };
      }
      return result;
    };

    // Only replace the function if it's not the S function (to avoid conflicts with fix-s-catch.js)
    if (functionName !== 'S' && functionName !== 'Qr') {
      object[propertyName] = function(...args) {
        try {
          const result = originalFunction.apply(this, args);
          return window._ensureCatchMethod ? window._ensureCatchMethod(result) : result;
        } catch (error) {
          console.error(`[PromiseCatchFix] Error in ${functionName}:`, error);
          // Return a safe value that has a catch method
          return Promise.resolve(null);
        }
      };

      // Copy properties from original function
      Object.assign(object[propertyName], originalFunction);
      object[propertyName]._promiseCatchFixPatched = true;
    }

    console.log(`[PromiseCatchFix] Successfully patched ${functionName}`);
  }

  function patchDashboardComponents() {
    // Wait for React to be available
    if (!window.React) {
      console.log('[PromiseCatchFix] React not available yet, will try again later');
      setTimeout(patchDashboardComponents, 500);
      return;
    }

    try {
      console.log('[PromiseCatchFix] Patching dashboard components...');

      // Check if React.createElement is already patched
      if (window.React.createElement._promiseCatchFixPatched) {
        console.log('[PromiseCatchFix] React.createElement already patched, skipping');
        return;
      }

      // Find and patch the problematic component
      // This is a generic approach since we don't know the exact component name
      const originalCreateElement = window.React.createElement;

      // Mark as patched to avoid double patching
      originalCreateElement._promiseCatchFixPatched = true;

      // Create a wrapped version that doesn't replace the original function
      window._safeCreateElement = function(type, props, ...children) {
        // Check if this is a component that might be causing the issue
        if (typeof type === 'function' && type.name) {
          // Check if this component is already wrapped
          if (type._promiseCatchFixWrapped) {
            return originalCreateElement(type, props, ...children);
          }

          const originalType = type;

          // Create a wrapped version of the component
          const wrappedType = function(props) {
            try {
              const result = originalType(props);

              // Ensure any Promise-like results have a catch method
              if (result && typeof result.then === 'function' && typeof result.catch !== 'function') {
                console.log(`[PromiseCatchFix] Adding catch method to ${originalType.name} result`);
                result.catch = function(onRejected) {
                  return Promise.resolve(result).catch(onRejected);
                };
              }

              return result;
            } catch (error) {
              console.error(`[PromiseCatchFix] Error in component ${originalType.name}:`, error);

              // Return a simple fallback UI
              return originalCreateElement('div', { className: 'error-fallback' },
                originalCreateElement('h3', null, 'Something went wrong'),
                originalCreateElement('p', null, 'The application encountered an error but is still running.'),
                originalCreateElement('button', {
                  onClick: () => window.location.reload(),
                  style: { padding: '8px 16px', margin: '10px 0', cursor: 'pointer' }
                }, 'Refresh Page')
              );
            }
          };

          // Copy static properties and name
          Object.assign(wrappedType, originalType);
          wrappedType.displayName = originalType.displayName || originalType.name;
          wrappedType._promiseCatchFixWrapped = true;

          return originalCreateElement(wrappedType, props, ...children);
        }

        return originalCreateElement(type, props, ...children);
      };

      // Specifically look for the Qr component mentioned in the error stack
      if (window.Qr) {
        console.log('[PromiseCatchFix] Found Qr component, patching it');
        patchSpecificFunction('Qr', window, 'Qr');
      }

      console.log('[PromiseCatchFix] Dashboard components patched successfully');
    } catch (error) {
      console.error('[PromiseCatchFix] Error patching dashboard components:', error);
    }
  }
})();
